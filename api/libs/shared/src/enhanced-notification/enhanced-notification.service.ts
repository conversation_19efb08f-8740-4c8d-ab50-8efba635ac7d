import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { NotificationStatus } from '../constants/notification.constant';
import { DrizzleService } from '../drizzle/drizzle.service';
import { hashObject } from '@app/shared/utils/hash.utils';
import { UserRepository } from '@/repositories/user.repository';
import { notification_types } from '@/db/schema/notification_system';
import { eq } from 'drizzle-orm';

import { NotificationTemplateService } from '../notification/notification-template.service';
import { NotificationTypeService } from '../notification/notification-type.service';
import { NotificationPreferenceService } from './notification-preference.service';
import { NotificationHistoryService } from '../notification/notification-history.service';
import { ScheduledNotificationService } from './scheduled-notification.service';
import { DeviceTokenService } from './device-token.service';
import { NotificationChannelService } from './notification-channel.service';
import { EnhancedNotificationQueueService } from '../enhanced-notification-queue/enhanced-notification-queue.service';
import {
  NotificationHistoryQueryDto,
  ScheduledNotificationsQueryDto,
  ScheduleNotificationDto,
} from './dto/enhanced-notification.dto';
import { Subject } from 'rxjs';

@Injectable()
export class EnhancedNotificationService {
  private readonly logger = new Logger(EnhancedNotificationService.name);

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly templateService: NotificationTemplateService,
    private readonly typeService: NotificationTypeService,
    private readonly preferenceService: NotificationPreferenceService,
    private readonly historyService: NotificationHistoryService,
    private readonly scheduledService: ScheduledNotificationService,
    private readonly deviceTokenService: DeviceTokenService,
    private readonly channelService: NotificationChannelService,
    private readonly enhancedNotificationQueueService: EnhancedNotificationQueueService,
    private readonly userRepository: UserRepository,
  ) {}

  /**
   * Send notification to a user
   */
  async sendNotification(params: {
    notificationTypeId: string;
    userId: string;
    data?: Record<string, any>;
    channels?: string[];
    jobId?: string;
  }): Promise<void> {
    const { notificationTypeId, userId, data = {}, channels, jobId } = params;

    try {
      // Validate that the user is active and not deleted
      const user = await this.userRepository.validateActiveUser(userId);

      // Skip notification if user validation fails
      if (!user) {
        return;
      }

      // Get notification type
      const notificationType =
        await this.typeService.getNotificationType(notificationTypeId);

      if (!notificationType) {
        throw new NotFoundException(
          `Notification type with ID ${notificationTypeId} not found`,
        );
      }

      // Get notification template
      const template = await this.templateService.getNotificationTemplate(
        notificationType.template_id,
      );

      if (!template) {
        throw new NotFoundException(
          `Template with ID ${notificationType.template_id} not found`,
        );
      }

      // Get user notification preferences
      const preferences =
        await this.preferenceService.getUserNotificationPreference(
          userId,
          notificationTypeId,
        );

      // Determine which channels to use
      const effectiveChannels = channels || notificationType.default_channels;

      // Compile notification title and body
      const title = this.templateService.compileTemplate(
        template.title_template,
        data,
      );
      const body = this.templateService.compileTemplate(
        template.body_template,
        data,
      );

      // Send notifications through each channel
      const enabledChannels = [];

      // Check if email is enabled
      if (effectiveChannels.includes('email')) {
        // Only log email preference issues in debug mode or when there's a problem
        if (!preferences.email_enabled || process.env.EMAIL_TOGGLE !== 'ON') {
          this.logger.debug(
            `Email notifications skipped for user ${userId} (preference: ${
              preferences.email_enabled ? 'enabled' : 'disabled'
            }, toggle: ${process.env.EMAIL_TOGGLE})`,
          );
        }

        if (preferences.email_enabled && process.env.EMAIL_TOGGLE === 'ON') {
          enabledChannels.push('email');

          try {
            // Compile email subject and body
            const emailSubject = this.templateService.compileTemplate(
              template.email_subject_template || title,
              data,
            );
            const emailBody = this.templateService.compileTemplate(
              template.email_body_template || body,
              data,
            );

            // Generate a unique job ID for this email to prevent duplicates
            const emailJobId = jobId
              ? `${jobId}-email`
              : `email-direct-${userId}-${notificationTypeId}-${hashObject({ data, template: template.email_subject_template || title })}`;

            await this.channelService.sendEmailNotification(
              userId,
              emailSubject,
              emailBody,
              {
                ...data,
                module: notificationType.module,
                type: notificationType.code,
                emailJobId,
              },
            );
          } catch (emailError: any) {
            this.logger.error(
              `Failed to send email notification to user ${userId}:`,
              emailError?.stack,
            );
            // Continue with other channels
          }
        } else {
          this.logger.warn(
            `Email notifications skipped for user ${userId}:
            - User preference: ${preferences.email_enabled ? 'ENABLED' : 'DISABLED'}
            - EMAIL_TOGGLE: ${process.env.EMAIL_TOGGLE}
            To enable emails, ensure both user preferences and EMAIL_TOGGLE are enabled.`,
          );
        }
      }

      // Check if push is enabled
      if (effectiveChannels.includes('push') && preferences.push_enabled) {
        enabledChannels.push('push');
        try {
          // Send push notification
          await this.channelService.sendPushNotification(userId, title, body, {
            ...data,
            module: notificationType.module,
            type: notificationType.code,
            notification_id: data.notification_id || '',
          });
        } catch (pushError: any) {
          this.logger.error(
            `Failed to send push notification to user ${userId}:`,
            pushError?.stack,
          );
          // Continue with other channels
        }
      }

      // Check if in-app is enabled
      if (effectiveChannels.includes('in_app') && preferences.in_app_enabled) {
        enabledChannels.push('in_app');
        try {
          // Send in-app notification
          await this.channelService.sendInAppNotification(
            userId,
            title,
            body,
            {
              ...data,
              module: notificationType.module,
              type: notificationType.code,
            },
            notificationType.module || 'general',
          );
        } catch (inAppError: any) {
          this.logger.error(
            `Failed to send in-app notification to user ${userId}:`,
            inAppError?.stack,
          );
          // Continue with other channels
        }
      }

      // Log notification
      await this.historyService.logNotification({
        notificationTypeId,
        userId,
        title,
        body,
        data: {
          ...data,
          module: notificationType.module,
          type: notificationType.code,
        },
        channels: enabledChannels,
        status: NotificationStatus.SENT,
      });
    } catch (error: any) {
      this.logger.error(
        `Failed to send notification to user ${userId}:`,
        error?.stack,
      );

      // Log error
      await this.historyService.logNotification({
        notificationTypeId,
        userId,
        title: 'Notification Error',
        body: 'Failed to send notification',
        data,
        channels: [],
        status: 'error',
        error: error?.message || 'Unknown error',
      });

      throw error;
    }
  }

  /**
   * Send notification to multiple users
   */
  async sendBulkNotifications(params: {
    notificationTypeId: string;
    userIds: string[];
    data?: Record<string, any>;
    channels?: string[];
    jobId?: string;
  }): Promise<void> {
    const { notificationTypeId, userIds, data = {}, channels } = params;

    // Process notifications directly

    // Fall back to sending notifications one by one
    for (const userId of userIds) {
      try {
        // Generate a unique job ID for each user to prevent duplicates
        const userJobId = params.jobId
          ? `${params.jobId}-user-${userId}`
          : `notification-${notificationTypeId}-${userId}-${Date.now()}`;

        await this.sendNotification({
          notificationTypeId,
          userId,
          data,
          channels,
          jobId: userJobId,
        });
      } catch (error: any) {
        this.logger.error(
          `Failed to send notification to user ${userId}:`,
          error?.stack,
        );
        // Continue with other users
      }
    }
  }

  /**
   * Send a notification directly to a user without creating more jobs
   * This method is used by the NotificationProcessor to avoid recursive job creation
   * @param params Notification parameters
   */
  async sendDirectNotification(params: {
    userId: string;
    notificationTypeId: string;
    data?: Record<string, any>;
    channels?: string[];
    jobId?: string;
  }): Promise<void> {
    const {
      userId,
      notificationTypeId,
      data = {},
      channels = ['push'],
      jobId,
    } = params;

    try {
      // Validate that the user is active and not deleted
      const user = await this.userRepository.validateActiveUser(userId);

      // Skip notification if user validation fails
      if (!user) {
        return;
      }

      // Get notification type
      const notificationType =
        await this.typeService.getNotificationType(notificationTypeId);

      if (!notificationType) {
        throw new NotFoundException(
          `Notification type with ID ${notificationTypeId} not found`,
        );
      }

      // Get notification template
      const template = await this.templateService.getNotificationTemplate(
        notificationType.template_id,
      );

      if (!template) {
        throw new NotFoundException(
          `Template with ID ${notificationType.template_id} not found`,
        );
      }

      // Get user notification preferences
      const preferences =
        await this.preferenceService.getUserNotificationPreference(
          userId,
          notificationTypeId,
        );

      // Determine which channels to use
      const effectiveChannels = channels || notificationType.default_channels;

      // Compile notification title and body
      const title = this.templateService.compileTemplate(
        template.title_template,
        data,
      );
      const body = this.templateService.compileTemplate(
        template.body_template,
        data,
      );

      // Send notifications through each channel
      const enabledChannels = [];

      // Check if email is enabled
      if (effectiveChannels.includes('email')) {
        // Only log email preference issues in debug mode or when there's a problem
        if (!preferences.email_enabled || process.env.EMAIL_TOGGLE !== 'ON') {
          this.logger.debug(
            `Email notifications skipped for user ${userId} (preference: ${
              preferences.email_enabled ? 'enabled' : 'disabled'
            }, toggle: ${process.env.EMAIL_TOGGLE})`,
          );
        }

        if (preferences.email_enabled && process.env.EMAIL_TOGGLE === 'ON') {
          enabledChannels.push('email');

          try {
            // Compile email subject and body
            const emailSubject = this.templateService.compileTemplate(
              template.email_subject_template || title,
              data,
            );
            const emailBody = this.templateService.compileTemplate(
              template.email_body_template || body,
              data,
            );

            // Send email notification with minimal logging
            // Generate a unique job ID for this email to prevent duplicates
            const emailJobId = jobId
              ? `${jobId}-email`
              : `email-direct-${userId}-${notificationTypeId}-${hashObject({ data, template: template.email_subject_template || title })}`;

            await this.channelService.sendEmailNotification(
              userId,
              emailSubject,
              emailBody,
              {
                ...data,
                module: notificationType.module,
                type: notificationType.code,
                emailJobId, // Pass the deduplicated job ID for tracking/deduplication
              },
            );
          } catch (emailError: any) {
            this.logger.error(
              `Failed to send email notification to user ${userId}:`,
              emailError?.stack,
            );
            // Continue with other channels
          }
        } else {
          this.logger.warn(
            `Email notifications skipped for user ${userId}:
            - User preference: ${preferences.email_enabled ? 'ENABLED' : 'DISABLED'}
            - EMAIL_TOGGLE: ${process.env.EMAIL_TOGGLE}
            To enable emails, ensure both user preferences and EMAIL_TOGGLE are enabled.`,
          );
        }
      }

      // Check if push is enabled
      if (effectiveChannels.includes('push') && preferences.push_enabled) {
        enabledChannels.push('push');
        try {
          // Send push notification
          await this.channelService.sendPushNotification(userId, title, body, {
            ...data,
            module: notificationType.module,
            type: notificationType.code,
            notification_id: data.notification_id || '',
          });
        } catch (pushError: any) {
          this.logger.error(
            `Failed to send push notification to user ${userId}:`,
            pushError?.stack,
          );
          // Continue with other channels
        }
      }

      // Check if in-app is enabled
      if (effectiveChannels.includes('in_app') && preferences.in_app_enabled) {
        enabledChannels.push('in_app');
        try {
          // Send in-app notification
          await this.channelService.sendInAppNotification(
            userId,
            title,
            body,
            {
              ...data,
              module: notificationType.module,
              type: notificationType.code,
            },
            notificationType.module || 'general',
          );
        } catch (inAppError: any) {
          this.logger.error(
            `Failed to send in-app notification to user ${userId}:`,
            inAppError?.stack,
          );
          // Continue with other channels
        }
      }

      // Log notification
      await this.historyService.logNotification({
        notificationTypeId,
        userId,
        title,
        body,
        data: {
          ...data,
          module: notificationType.module,
          type: notificationType.code,
        },
        channels: enabledChannels,
        status: NotificationStatus.SENT,
      });
    } catch (error: any) {
      this.logger.error(
        `Failed to send direct notification to user ${userId}:`,
        error?.stack,
      );
      throw error;
    }
  }

  /**
   * For backward compatibility: send notification to a user
   */
  async sendNotificationToUser(params: {
    userId: string;
    notificationTypeId: string;
    data?: Record<string, any>;
    overridePreferences?: boolean;
    channels?: string[];
    jobId?: string;
  }): Promise<void> {
    const { userId, notificationTypeId, data = {}, channels, jobId } = params;
    return this.sendNotification({
      notificationTypeId,
      userId,
      data,
      channels,
      jobId,
    });
  }

  /**
   * For backward compatibility: send notification to multiple users
   */
  async sendNotificationToUsers(params: {
    notificationTypeId: string;
    data?: Record<string, any>;
    targetAudience: {
      roles?: string[];
      filters?: {
        clubId?: string;
        institutionIds?: string[];
        countryIds?: string[];
        userState?: string;
        postId?: string;
      };
    };
    channels?: string[];
    jobId?: string;
  }): Promise<string> {
    // Check if email channel is included and EMAIL_TOGGLE is ON
    if (
      (params.channels?.includes('email') || !params.channels?.length) &&
      process.env.EMAIL_TOGGLE !== 'ON'
    ) {
    }

    try {
      // Use the TypeService to get the notification type
      const notificationType = await this.typeService.getNotificationType(
        params.notificationTypeId,
      );

      if (!notificationType) {
        throw new NotFoundException(
          `Notification type with ID ${params.notificationTypeId} not found`,
        );
      }

      // Ensure data has module and type information
      const enhancedData = {
        ...(params.data || {}),
        module: params.data?.module || notificationType.module,
        type: params.data?.type || notificationType.code,
      };

      // Ensure channels is an array and includes appropriate channels
      const channels = params.channels?.length
        ? [...params.channels]
        : ['push'];

      // For post-related notifications, ensure email is included
      if (
        (notificationType.code === 'new_post' ||
          notificationType.code === 'new_event' ||
          notificationType.code === 'new_opportunity' ||
          notificationType.module === 'post') &&
        !channels.includes('email')
      ) {
        channels.push('email');
      }

      // Generate a unique job ID if not provided
      const jobId =
        params.jobId ||
        `bulk-notification-${params.notificationTypeId}-${Date.now()}`;

      // Create a copy of the target audience to avoid modifying the original
      const targetAudience = {
        roles: params.targetAudience.roles
          ? [...params.targetAudience.roles]
          : undefined,
        filters: params.targetAudience.filters
          ? { ...params.targetAudience.filters }
          : {},
      };

      // Always ensure userState is set to 'active' to only send to active users
      // Force userState to 'active' regardless of what was provided
      targetAudience.filters.userState = 'active';

      // Use the EnhancedNotificationQueueService to create a single bulk job
      // This will be processed by the NotificationProcessor to send to all matching users

      // Use the EnhancedNotificationQueueService to create a bulk notification job
      // This will be processed by the NotificationProcessor
      const queueJobId =
        await this.enhancedNotificationQueueService.sendNotificationToUser({
          notificationTypeId: params.notificationTypeId,
          data: enhancedData,
          targetAudience,
          channels,
          jobId,
        });

      return queueJobId;
    } catch (error: any) {
      this.logger.error(
        `Failed to send notifications to users: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  // Template methods
  async getNotificationTemplates() {
    return this.templateService.getNotificationTemplates();
  }

  async createNotificationTemplate(data: any) {
    return this.templateService.createNotificationTemplate(data);
  }

  async updateNotificationTemplate(id: string, data: any) {
    return this.templateService.updateNotificationTemplate(id, data);
  }

  // Type methods
  async getNotificationTypes() {
    return this.typeService.getNotificationTypes();
  }

  /**
   * Create notification type
   * This method allows creating notification types with proper code formatting
   */
  async createNotificationType(data: {
    code?: string;
    name: string;
    description: string;
    module: string;
    template_id: string;
    default_channels?: string[];
  }) {
    return this.typeService.createNotificationType(data, false);
  }

  async updateNotificationType(id: string, data: any) {
    return this.typeService.updateNotificationType(id, data);
  }

  // Preference methods
  async getUserNotificationPreferences(userId: string) {
    return this.preferenceService.getUserNotificationPreferences(userId);
  }

  async updateUserNotificationPreferences(userId: string, preferences: any[]) {
    return this.preferenceService.updateUserNotificationPreferences(
      userId,
      preferences,
    );
  }

  // History methods
  async getUserNotificationHistory(
    userId: string,
    params: NotificationHistoryQueryDto,
  ) {
    return this.historyService.getUserNotificationHistory(userId, params);
  }

  async markNotificationAsRead(userId: string, notificationId: string) {
    return this.historyService.markNotificationAsRead(userId, notificationId);
  }

  async getNotificationLogById(userId: string, logId: string) {
    return this.historyService.getNotificationLogById(userId, logId);
  }

  /**
   * Delete a notification log
   * @param userId The user ID
   * @param logId The notification log ID
   */
  async deleteNotificationLog(userId: string, logId: string): Promise<void> {
    return this.historyService.deleteNotificationLog(userId, logId);
  }

  // Scheduled notification methods
  async scheduleNotification(params: any) {
    return this.scheduledService.scheduleNotification(params);
  }

  async getScheduledNotifications(params: ScheduledNotificationsQueryDto) {
    return this.scheduledService.getScheduledNotifications(params);
  }

  async cancelScheduledNotification(id: string) {
    return this.scheduledService.cancelScheduledNotification(id);
  }

  async updateScheduledNotification(
    id: string,
    data: Partial<ScheduleNotificationDto & { updatedBy: string }>,
  ) {
    return this.scheduledService.updateScheduledNotification(id, data);
  }

  async processScheduledNotifications() {
    const dueNotifications =
      await this.scheduledService.getDueScheduledNotifications();

    for (const notification of dueNotifications) {
      try {
        // Update status to processing
        await this.scheduledService.updateScheduledNotificationStatus(
          notification.id,
          'processing',
        );

        // Extract target audience
        const targetAudience = notification.target_audience as {
          roles?: string[];
          filters?: {
            clubId?: string;
            institutionIds?: string[];
            countryIds?: string[];
            userState?: string;
          };
        };

        // Ensure we only send to active users, but preserve existing userState if provided
        if (!targetAudience.filters) {
          targetAudience.filters = {};
        }
        if (!targetAudience.filters.userState) {
          targetAudience.filters.userState = 'active';
        }

        // Send notification to target audience using the existing sendNotificationToUsers method
        const jobId = await this.sendNotificationToUsers({
          notificationTypeId: notification.notification_type_id,
          data: {
            ...((notification.data as Record<string, any>) || {}),
            scheduledNotificationId: notification.id,
            isScheduled: true,
            originalScheduledFor: notification.scheduled_for,
            title: notification.title,
            body: notification.body,
          },
          targetAudience,
          channels: notification.channels,
          jobId: `scheduled-${notification.id}-${Date.now()}`,
        });

        this.logger.log(
          `Scheduled notification ${notification.id} queued successfully with job ID: ${jobId}`,
        );

        // Update status to sent
        await this.scheduledService.updateScheduledNotificationStatus(
          notification.id,
          NotificationStatus.SENT,
          {
            ...((notification.data as Record<string, any>) || {}),
            processedAt: new Date().toISOString(),
            jobId,
          },
        );

        this.logger.log(
          `Scheduled notification ${notification.id} marked as sent`,
        );
      } catch (error: any) {
        this.logger.error(
          `Failed to process scheduled notification ${notification.id}:`,
          error?.stack,
        );

        // Update status to failed
        await this.scheduledService.updateScheduledNotificationStatus(
          notification.id,
          'failed',
          {
            ...((notification.data as Record<string, any>) || {}),
            error: error?.message,
            failedAt: new Date().toISOString(),
          },
        );
      }
    }

    this.logger.log(
      `Completed processing ${dueNotifications.length} scheduled notifications`,
    );
  }

  // Device token methods
  async registerDeviceToken(userId: string, token: string, deviceType: string) {
    return this.deviceTokenService.registerDeviceToken(
      userId,
      token,
      deviceType,
    );
  }

  async unregisterDeviceToken(userId: string, token: string) {
    return this.deviceTokenService.unregisterDeviceToken(userId, token);
  }

  // Channel methods
  getInAppNotificationStream(eventType: string): Subject<any> | undefined {
    return this.channelService.getInAppNotificationSubject(eventType);
  }

  /**
   * Get in-app notification subject for a module (alias for getInAppNotificationStream)
   * @param module The module name
   * @returns The Subject for the module, or undefined if not found
   */
  getInAppNotificationSubject(module: string): Subject<any> | undefined {
    return this.getInAppNotificationStream(module);
  }

  /**
   * Get notification type by ID
   * @param id The notification type ID
   * @returns The notification type
   */
  async getNotificationType(id: string): Promise<any> {
    return this.typeService.getNotificationType(id);
  }

  /**
   * Clean up old notification logs
   * @param daysToKeep Number of days to keep logs for
   */
  async cleanupOldNotificationLogs(daysToKeep: number = 30): Promise<void> {
    await this.historyService.cleanupOldNotificationLogs(daysToKeep);
  }

  /**
   * Execute a direct SQL query using the drizzle service
   * This method is used by the NotificationProcessor to query users directly
   * @param query SQL query to execute
   * @returns Query result
   */
  async executeQuery(query: string): Promise<any> {
    try {
      return await this.drizzle.db.execute(query);
    } catch (error: any) {
      this.logger.error(
        `Failed to execute query: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async getMobileNotificationPreferences(userId: string) {
    return this.preferenceService.getMobileNotificationPreferences(userId);
  }

  async updateMobileNotificationPreferences(
    userId: string,
    preferences: any[],
  ) {
    return this.preferenceService.updateMobileNotificationPreferences(
      userId,
      preferences,
    );
  }

  async getNotificationTypesByModule(module: string) {
    // Get notification types for the specified module directly from database
    // This is a workaround for TypeScript compilation issues
    this.logger.debug(`Fetching notification types for module ${module}`);

    return this.drizzle.db
      .select()
      .from(notification_types)
      .where(eq(notification_types.module, module));
  }
}
