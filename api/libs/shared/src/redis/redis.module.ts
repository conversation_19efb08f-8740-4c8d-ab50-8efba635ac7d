import { Global, Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { CacheService } from './cache.service';
import {
  ConfigurableRedisModule,
  RedisOptions,
  RedisStandaloneOptions,
  RedisTimeoutConfig,
  RedisConnectionConfig,
} from './redis.module-definition';
import { RedisService } from './redis.service';
import { RedisConnectionManagerService } from './redis-connection-manager.service';
import { RedisReadinessService } from './redis-readiness.service';

/**
 * Creates Redis configuration directly without factory pattern
 */
function createRedisConfig(configService: ConfigService): RedisOptions {
  const mode = configService.get('REDIS_MODE', 'single').toLowerCase() as
    | 'single'
    | 'cluster'
    | 'auto';
  const isDevelopment = process.env.NODE_ENV === 'development';

  // Create timeout configuration
  const timeouts: RedisTimeoutConfig = {
    commandTimeout: parseInt(
      configService.get('REDIS_COMMAND_TIMEOUT_MS') ||
        (isDevelopment ? '20000' : '15000'),
      10,
    ),
    connectionTimeout: parseInt(
      configService.get('REDIS_CONNECTION_TIMEOUT_MS') ||
        (isDevelopment ? '15000' : '10000'),
      10,
    ),
    disconnectTimeout: isDevelopment ? 3000 : 5000,
  };

  // Create connection configuration
  const connectionConfig: RedisConnectionConfig = {
    maxRetriesPerRequest: null, // Required for BullMQ compatibility
    enableOfflineQueue: true,
    enableReadyCheck: false,
    lazyConnect: true,
    family: 4,
    keepAlive: true,
    keepAliveInitialDelay: isDevelopment ? 5000 : 10000,
    retryDelayOnFailover: 100,
    enableAutoPipelining: false,
  };

  const config: RedisOptions = {
    mode,
    healthCheckInterval: parseInt(
      configService.get('REDIS_HEALTHCHECK_INTERVAL_MS', '30000'),
      10,
    ),
    fallbackToSingle: true,
    timeouts,
    connectionConfig,
  };

  // Create single configuration
  const singleConfig: RedisStandaloneOptions = {
    host: configService.get('REDIS_HOST', 'localhost'),
    port: parseInt(configService.get('REDIS_PORT', '6379'), 10),
    password: configService.get('REDIS_PASSWORD', undefined),
    username: configService.get('REDIS_USERNAME', undefined),
    db: parseInt(configService.get('REDIS_DB', '0'), 10),
    tls: configService.get('REDIS_TLS') === 'true' ? {} : undefined,
  };

  switch (mode) {
    case 'single':
      config.single = singleConfig;
      break;
    case 'cluster':
      const nodesStr = configService.get('REDIS_CLUSTER_NODES', '');
      if (!nodesStr) {
        throw new Error('REDIS_CLUSTER_NODES must be defined for cluster mode');
      }

      const nodes = nodesStr
        .split(',')
        .map((node: string) => {
          const [host, port] = node.trim().split(':');
          if (!host || !port) {
            throw new Error(`Invalid cluster node format: ${node}`);
          }
          return { host, port: parseInt(port, 10) };
        })
        .filter(
          (node: { host: string; port: number }) => node.host && node.port,
        );

      config.cluster = {
        nodes,
        redisOptions: {
          password: configService.get('REDIS_PASSWORD', undefined),
          tls: configService.get('REDIS_TLS') === 'true' ? {} : undefined,
        },
      };
      break;
    case 'auto':
      config.single = singleConfig;
      const clusterNodesStr = configService.get('REDIS_CLUSTER_NODES');
      if (clusterNodesStr) {
        const clusterNodes = clusterNodesStr
          .split(',')
          .map((node: string) => {
            const [host, port] = node.trim().split(':');
            if (!host || !port) {
              return null;
            }
            return { host, port: parseInt(port, 10) };
          })
          .filter(
            (
              node: { host: string; port: number } | null,
            ): node is { host: string; port: number } =>
              node !== null && !!node.host && !!node.port,
          );

        if (clusterNodes.length > 0) {
          config.cluster = {
            nodes: clusterNodes,
            redisOptions: {
              password: configService.get('REDIS_PASSWORD', undefined),
              tls: configService.get('REDIS_TLS') === 'true' ? {} : undefined,
            },
          };
        }
      }
      break;
  }

  return config;
}

@Global()
@Module({
  exports: [
    RedisService,
    CacheService,
    RedisConnectionManagerService,
    RedisReadinessService,
  ],
  providers: [
    RedisConnectionManagerService,
    RedisReadinessService,
    {
      provide: 'REDIS_OPTIONS',
      useFactory: (configService: ConfigService) => {
        return createRedisConfig(configService);
      },
      inject: [ConfigService],
    },
    RedisService,
    CacheService,
  ],
})
export class RedisModule extends ConfigurableRedisModule {
  static registerAsync(options: any) {
    return {
      module: RedisModule,
      imports: options.imports || [],
      providers: [
        RedisConnectionManagerService,
        RedisReadinessService,
        {
          provide: 'REDIS_OPTIONS',
          useFactory: async (configService: ConfigService) => {
            return createRedisConfig(configService);
          },
          inject: [ConfigService],
        },
        RedisService,
        CacheService,
      ],
      exports: [
        RedisService,
        CacheService,
        RedisConnectionManagerService,
        RedisReadinessService,
      ],
    };
  }
}
