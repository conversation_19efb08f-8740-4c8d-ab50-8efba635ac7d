import { EnvConfig } from '../dto/env-config.dto';

/**
 * Validates Redis configuration for non-local environments
 * Ensures all required Redis variables  are present from Parameter Store
 */
export function validateRedisConfigForEnvironment(
  envConfig: EnvConfig,
  environment: string,
): void {
  const requiredRedisVars = [
    { key: 'REDIS_HOST', value: envConfig.REDIS_HOST },
    { key: 'REDIS_PORT', value: envConfig.REDIS_PORT },
    { key: 'REDIS_MODE', value: envConfig.REDIS_MODE },
    {
      key: 'REDIS_HEALTHCHECK_INTERVAL_MS',
      value: envConfig.REDIS_HEALTHCHECK_INTERVAL_MS,
    },
  ];

  const missingVars: string[] = [];

  for (const { key, value } of requiredRedisVars) {
    if (!value) {
      missingVars.push(key);
    }
  }

  if (missingVars.length > 0) {
    throw new Error(
      `Redis configuration validation failed for ${environment} environment. ` +
        `Missing required variables from Parameter Store: ${missingVars.join(', ')}. ` +
        `All Redis configuration must be loaded from Parameter Store for non-local environments.`,
    );
  }
}

/**
 * Validates Redis configuration using ConfigService for non-local environments
 * Used in Redis module where ConfigService is available
 */
export function validateRedisConfigWithConfigService(
  configService: { get: (key: string) => string | undefined },
  environment: string,
): void {
  const requiredRedisVars = [
    'REDIS_HOST',
    'REDIS_PORT',
    'REDIS_MODE',
    'REDIS_COMMAND_TIMEOUT_MS',
    'REDIS_CONNECTION_TIMEOUT_MS',
    'REDIS_HEALTHCHECK_INTERVAL_MS',
  ];

  const missingVars: string[] = [];

  for (const varName of requiredRedisVars) {
    const value = configService.get(varName);
    if (!value) {
      missingVars.push(varName);
    }
  }

  if (missingVars.length > 0) {
    throw new Error(
      `Redis configuration validation failed for ${environment} environment. ` +
        `Missing required variables from Parameter Store: ${missingVars.join(', ')}. ` +
        `All Redis configuration must be loaded from Parameter Store for non-local environments.`,
    );
  }
}
