import { RedisService } from './redis.service';
import Redis from 'ioredis';
import type { RedisOptions } from './redis.module-definition';
import { RedisConnectionManagerService } from './redis-connection-manager.service';
import { UnifiedRedisConfigFactory } from './unified-redis-config.factory';

// Mock ioredis
jest.mock('ioredis', () => {
  const mockRedis = jest.fn().mockImplementation(() => ({
    on: jest.fn(),
    ping: jest.fn(),
    set: jest.fn(),
    get: jest.fn(),
    del: jest.fn(),
    flushdb: jest.fn(),
    keys: jest.fn(),
    info: jest.fn(),
    disconnect: jest.fn(),
    connect: jest.fn(),
    quit: jest.fn(),
  }));

  const mockCluster = jest.fn().mockImplementation(() => ({
    on: jest.fn(),
    ping: jest.fn(),
    set: jest.fn(),
    get: jest.fn(),
    del: jest.fn(),
    keys: jest.fn(),
    info: jest.fn(),
    disconnect: jest.fn(),
    connect: jest.fn(),
    quit: jest.fn(),
  }));

  return {
    __esModule: true,
    default: mockRedis,
    Cluster: mockCluster,
  };
});

// Mock Logger
jest.mock('@nestjs/common', () => {
  const original = jest.requireActual('@nestjs/common');
  return {
    ...original,
    Logger: jest.fn().mockImplementation(() => ({
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    })),
  };
});

describe('RedisService', () => {
  let service: RedisService;
  let mockRedis: jest.Mocked<Redis>;
  let mockConnectionManager: jest.Mocked<RedisConnectionManagerService>;
  let mockConfigFactory: jest.Mocked<UnifiedRedisConfigFactory>;
  const MockRedis = Redis as jest.MockedClass<typeof Redis>;

  let eventHandlers: Record<string, (...args: any[]) => void> = {};

  const mockOptions: RedisOptions = {
    mode: 'single',
    healthCheckInterval: 30000,
    fallbackToSingle: true,
    timeouts: {
      commandTimeout: 15000,
      connectionTimeout: 10000,
      disconnectTimeout: 3000,
    },
    connectionConfig: {
      maxRetriesPerRequest: null,
      enableOfflineQueue: true,
      enableReadyCheck: false,
      lazyConnect: true,
      family: 4,
      keepAlive: true,
      keepAliveInitialDelay: 10000,
      retryDelayOnFailover: 100,
      enableAutoPipelining: false,
    },
    single: {
      host: 'localhost',
      port: 6379,
      password: 'test',
      username: 'user',
      db: 0,
    },
  };

  beforeEach(() => {
    // Reset event handlers for each test
    eventHandlers = {};

    // Create a more complete mock Redis client
    mockRedis = {
      on: jest.fn().mockImplementation((event, handler) => {
        eventHandlers[event] = handler;
        return mockRedis;
      }),
      ping: jest.fn().mockResolvedValue('PONG'),
      set: jest.fn().mockResolvedValue('OK'),
      get: jest.fn().mockResolvedValue(null),
      del: jest.fn().mockResolvedValue(1),
      flushdb: jest.fn().mockResolvedValue('OK'),
      keys: jest.fn().mockResolvedValue([]),
      info: jest.fn().mockResolvedValue('role:master'),
      disconnect: jest.fn(),
      connect: jest.fn().mockResolvedValue(undefined),
      quit: jest.fn().mockResolvedValue('OK'),
    } as any;

    MockRedis.mockImplementation(() => mockRedis);

    // Create mock dependencies
    mockConnectionManager = {
      registerConnection: jest.fn(),
      isConnectionHealthy: jest.fn().mockResolvedValue(true),
      getConnection: jest.fn(),
      getConnectionState: jest.fn(),
      getAllConnectionStates: jest.fn(),
      onApplicationShutdown: jest.fn(),
    } as any;

    mockConfigFactory = {
      createRetryStrategy: jest.fn().mockReturnValue(() => 1000),
      createReconnectOnErrorHandler: jest.fn().mockReturnValue(() => true),
      createUnifiedRedisConfig: jest.fn(),
      createBullMQConnection: jest.fn(),
      createBullMQOptions: jest.fn(),
      createBullMQConfig: jest.fn(),
    } as any;

    // Mock setInterval and clearInterval
    jest.useFakeTimers();

    // Set environment variables for testing
    process.env.NODE_ENV = 'development';
    process.env.REDIS_LOG_LEVEL = 'debug';
    process.env.REDIS_COMMAND_TIMEOUT_MS = '20000';
    process.env.REDIS_CONNECTION_TIMEOUT_MS = '20000';

    // Create service directly instead of using NestJS testing module
    service = new RedisService(
      mockOptions,
      mockConnectionManager,
      mockConfigFactory,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.useRealTimers();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should initialize Redis with correct options', () => {
    expect(Redis).toHaveBeenCalledWith(
      expect.objectContaining({
        // Basic connection settings from mockOptions.single
        host: mockOptions.single?.host,
        port: mockOptions.single?.port,
        username: mockOptions.single?.username,
        password: mockOptions.single?.password,
        db: mockOptions.single?.db,

        // Enhanced timeout settings
        maxRetriesPerRequest: null, // Changed to null for BullMQ compatibility
        enableOfflineQueue: true,
        commandTimeout: 15000,
        connectTimeout: 10000,
        disconnectTimeout: 3000,
        enableReadyCheck: false,
        lazyConnect: true,
        family: 4,
        keepAlive: true,
        keepAliveInitialDelay: 10000,
        showFriendlyErrorStack: expect.any(Boolean),

        // Functions
        retryStrategy: expect.any(Function),
        reconnectOnError: expect.any(Function),
      }),
    );
  });

  describe('event handlers', () => {
    it('should set up event listeners', () => {
      expect(mockRedis.on).toHaveBeenCalledWith(
        'connect',
        expect.any(Function),
      );
      expect(mockRedis.on).toHaveBeenCalledWith('ready', expect.any(Function));
      expect(mockRedis.on).toHaveBeenCalledWith('error', expect.any(Function));
      expect(mockRedis.on).toHaveBeenCalledWith('end', expect.any(Function));
      expect(mockRedis.on).toHaveBeenCalledWith(
        'reconnecting',
        expect.any(Function),
      );
    });

    it('should set isConnected to true on connect event', () => {
      // Trigger the connect event
      if (eventHandlers['connect']) {
        eventHandlers['connect']();
        expect(service.isConnected).toBe(true);
      }
    });

    it('should set isConnected to true on ready event', () => {
      // Trigger the ready event
      if (eventHandlers['ready']) {
        eventHandlers['ready']();
        expect(service.isConnected).toBe(true);
      }
    });

    it('should set isConnected to false on error event', () => {
      // First set isConnected to true
      service['isConnected'] = true;

      // Trigger the error event
      if (eventHandlers['error']) {
        eventHandlers['error'](new Error('Test error'));
        expect(service.isConnected).toBe(false);
      }
    });

    it('should set isConnected to false on end event', () => {
      // First set isConnected to true
      service.isConnected = true;

      // Trigger the end event
      if (eventHandlers['end']) {
        eventHandlers['end']();
        expect(service.isConnected).toBe(false);
      }
    });

    it('should handle READONLY errors with controlled logging', () => {
      const logger = service['logger'];

      // Trigger the error event with a READONLY error
      if (eventHandlers['error']) {
        eventHandlers['error'](
          new Error("READONLY You can't write against a read only replica"),
        );
        expect(service.isConnected).toBe(false);
        // In development mode with debug logging, should log the warning
        expect(logger.warn).toHaveBeenCalledWith(
          'Redis READONLY error detected - attempting reconnection',
          expect.objectContaining({
            message: "READONLY You can't write against a read only replica",
            attempt: 1,
          }),
        );
      }
    });

    it('should handle ECONNREFUSED errors with reduced logging in production', () => {
      // Set to production mode
      const originalNodeEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      // Trigger multiple ECONNREFUSED errors
      if (eventHandlers['error']) {
        // First error should be logged
        eventHandlers['error'](new Error('ECONNREFUSED'));
        // Subsequent errors within 5 attempts should not be logged
        for (let i = 2; i <= 4; i++) {
          eventHandlers['error'](new Error('ECONNREFUSED'));
        }
        // The service should still track connection attempts
        expect(service.isConnected).toBe(false);
      }

      // Restore original NODE_ENV
      process.env.NODE_ENV = originalNodeEnv;
    });

    it('should handle reconnecting event with controlled logging', () => {
      const logger = service['logger'];

      // Trigger the reconnecting event
      if (eventHandlers['reconnecting']) {
        eventHandlers['reconnecting'](1000);
        // Should log in development mode with attempt information
        expect(logger.log).toHaveBeenCalledWith(
          'Redis reconnecting in 1000ms... (attempt 1/10)',
        );
      }
    });
  });

  describe('onApplicationBootstrap', () => {
    it('should test Redis connection on bootstrap', async () => {
      await service.onApplicationBootstrap();
      expect(mockRedis.ping).toHaveBeenCalled();
    });

    it('should not throw error if Redis connection fails on bootstrap', async () => {
      // Mock setTimeout to execute immediately to speed up the test
      jest.spyOn(global, 'setTimeout').mockImplementation((callback: any) => {
        if (typeof callback === 'function') {
          callback();
        }
        return {} as any;
      });

      const logger = service['logger'];
      jest.spyOn(logger, 'warn').mockImplementation();
      jest.spyOn(logger, 'error').mockImplementation();

      mockRedis.ping.mockRejectedValue(new Error('Connection failed'));

      // Should not throw error, just log warning and continue
      await expect(service.onApplicationBootstrap()).resolves.not.toThrow();

      expect(logger.warn).toHaveBeenCalledWith(
        'Redis connection not available during application bootstrap - will retry in background',
        expect.objectContaining({
          attempts: 2, // Updated to match the new bootstrap behavior (2 attempts)
          lastError: 'Connection failed',
          redisMode: 'single',
          timestamp: expect.any(String),
        }),
      );

      // Restore mocks
      jest.restoreAllMocks();
    });
  });

  describe('set', () => {
    it('should set value successfully', async () => {
      mockRedis.set.mockResolvedValue('OK');
      const result = await service.set('key', 'value');
      expect(result).toBe('OK');
      expect(mockRedis.set).toHaveBeenCalledWith('key', '"value"');
    });

    it('should set value with TTL', async () => {
      mockRedis.set.mockResolvedValue('OK');
      const result = await service.set('key', 'value', 60);
      expect(result).toBe('OK');
      expect(mockRedis.set).toHaveBeenCalledWith('key', '"value"', 'EX', 60);
    });

    it('should handle Redis errors by throwing them', async () => {
      const redisError = new Error('Redis connection failed');
      mockRedis.set.mockRejectedValueOnce(redisError);

      await expect(service.set('key', 'value')).rejects.toThrow(
        'Redis connection failed',
      );
    });
  });

  describe('get', () => {
    it('should get value successfully', async () => {
      mockRedis.get.mockResolvedValue('"value"');
      const result = await service.get('key');
      expect(result).toBe('value');
    });

    it('should return null for non-existent key', async () => {
      mockRedis.get.mockResolvedValue(null);
      const result = await service.get('key');
      expect(result).toBeNull();
    });

    it('should return non-JSON values as is', async () => {
      mockRedis.get.mockResolvedValue('not-json');
      const result = await service.get('key');
      expect(result).toBe('not-json');
    });

    it('should throw error when Redis get fails', async () => {
      mockRedis.get.mockRejectedValueOnce(new Error('Connection error'));
      await expect(service.get('key')).rejects.toThrow('Connection error');
    });
  });

  describe('del', () => {
    it('should delete key successfully', async () => {
      mockRedis.del.mockResolvedValue(1);
      const result = await service.del('key');
      expect(result).toBe(1);
    });

    it('should throw errors when del fails', async () => {
      const redisError = new Error('Redis connection failed');
      mockRedis.del.mockRejectedValueOnce(redisError);

      await expect(service.del('key')).rejects.toThrow(
        'Redis connection failed',
      );
    });
  });

  describe('clearAll', () => {
    it('should clear all keys', async () => {
      mockRedis.flushdb.mockResolvedValue('OK');
      const result = await service.clearAll();
      expect(result).toBe('OK');
    });

    it('should throw error for cluster mode', async () => {
      // Create a service with cluster mode
      const clusterOptions: RedisOptions = {
        mode: 'cluster',
        healthCheckInterval: 30000,
        fallbackToSingle: true,
        timeouts: {
          commandTimeout: 15000,
          connectionTimeout: 10000,
          disconnectTimeout: 3000,
        },
        connectionConfig: {
          maxRetriesPerRequest: null,
          enableOfflineQueue: true,
          enableReadyCheck: false,
          lazyConnect: true,
          family: 4,
          keepAlive: true,
          keepAliveInitialDelay: 10000,
          retryDelayOnFailover: 100,
          enableAutoPipelining: false,
        },
        cluster: {
          nodes: [{ host: 'localhost', port: 6379 }],
          redisOptions: {},
        },
      };
      const clusterService = new RedisService(
        clusterOptions,
        mockConnectionManager,
        mockConfigFactory,
      );

      await expect(clusterService.clearAll()).rejects.toThrow(
        'clearAll is not supported in cluster mode',
      );
    });

    it('should throw errors when flushdb fails', async () => {
      const redisError = new Error('Redis connection failed');
      mockRedis.flushdb.mockRejectedValueOnce(redisError);

      await expect(service.clearAll()).rejects.toThrow(
        'Redis connection failed',
      );
    });
  });

  describe('health check', () => {
    it('should return true when ping succeeds', async () => {
      mockRedis.ping.mockResolvedValue('PONG');
      const result = await service.isHealthy();
      expect(result).toBe(true);
    });

    it('should return false when ping fails', async () => {
      mockRedis.ping.mockRejectedValue(new Error('Connection failed'));
      mockConnectionManager.isConnectionHealthy.mockResolvedValue(false);
      const result = await service.isHealthy();
      expect(result).toBe(false);
    });
  });

  describe('keys', () => {
    it('should get keys successfully', async () => {
      mockRedis.keys.mockResolvedValue(['key1', 'key2']);
      const result = await service.keys('*');
      expect(result).toEqual(['key1', 'key2']);
      expect(mockRedis.keys).toHaveBeenCalledWith('*');
    });

    it('should throw error for cluster mode', async () => {
      // Create a service with cluster mode
      const clusterOptions: RedisOptions = {
        mode: 'cluster',
        healthCheckInterval: 30000,
        fallbackToSingle: true,
        timeouts: {
          commandTimeout: 15000,
          connectionTimeout: 10000,
          disconnectTimeout: 3000,
        },
        connectionConfig: {
          maxRetriesPerRequest: null,
          enableOfflineQueue: true,
          enableReadyCheck: false,
          lazyConnect: true,
          family: 4,
          keepAlive: true,
          keepAliveInitialDelay: 10000,
          retryDelayOnFailover: 100,
          enableAutoPipelining: false,
        },
        cluster: {
          nodes: [{ host: 'localhost', port: 6379 }],
          redisOptions: {},
        },
      };
      const clusterService = new RedisService(
        clusterOptions,
        mockConnectionManager,
        mockConfigFactory,
      );

      await expect(clusterService.keys('*')).rejects.toThrow(
        'keys is not supported in cluster mode',
      );
    });

    it('should throw errors when keys fails', async () => {
      const redisError = new Error('Redis connection failed');
      mockRedis.keys.mockRejectedValueOnce(redisError);

      await expect(service.keys('*')).rejects.toThrow(
        'Redis connection failed',
      );
    });
  });

  describe('shouldLog method', () => {
    it('should return true for error level when log level is info', () => {
      // Access the private method for testing
      const shouldLog = (service as any).shouldLog.bind(service);
      expect(shouldLog('error')).toBe(true);
    });

    it('should return true for warn level when log level is info', () => {
      const shouldLog = (service as any).shouldLog.bind(service);
      expect(shouldLog('warn')).toBe(true);
    });

    it('should return true for info level when log level is info', () => {
      const shouldLog = (service as any).shouldLog.bind(service);
      expect(shouldLog('info')).toBe(true);
    });

    it('should return true for debug level when log level is debug', () => {
      const shouldLog = (service as any).shouldLog.bind(service);
      expect(shouldLog('debug')).toBe(true);
    });
  });
});
