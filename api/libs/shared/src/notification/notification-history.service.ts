import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import {
  notification_logs,
  notification_types,
} from '@/db/schema/notification_system';
import { and, eq, sql } from 'drizzle-orm';
import { users } from '@/db/schema/users';
import { NotificationHistoryQueryDto } from '../enhanced-notification/dto/enhanced-notification.dto';
import { CacheService } from '@app/shared/cache/cache.service';
import {
  CACHE_PREFIXES,
  CACHE_TTL,
} from '@app/shared/constants/cache.constant';
import { createHash } from 'crypto';
import { NotificationEntityService } from './notification-entity.service';

// Define types based on the schema
type NotificationLog = typeof notification_logs.$inferSelect;

@Injectable()
export class NotificationHistoryService {
  private readonly logger = new Logger(NotificationHistoryService.name);

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly cacheService: CacheService,
    private readonly notificationEntityService: NotificationEntityService,
  ) {}

  /**
   * Get user notification history
   * @param userId - The user ID to get notifications for
   * @param params - Query parameters for filtering and pagination
   * @returns Paginated notification history with metadata
   */
  async getUserNotificationHistory(
    userId: string,
    params: NotificationHistoryQueryDto,
  ): Promise<{
    data: NotificationLog[];
    total: number;
    page: number;
    limit: number;
    pages: number;
  }> {
    const {
      page,
      limit,
      read,
      from_date,
      to_date,
      notification_type_id,
      channels,
      status,
      search,
      sort,
      order,
      module,
    } = params;

    // Generate a cache key based on the user ID and query parameters
    const cacheKey = await this.generateNotificationHistoryCacheKey(
      userId,
      params,
    );

    // Try to get data from cache first
    const cachedData = await this.cacheService.get(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [eq(notification_logs.user_id, userId)];

    if (read === true) {
      whereConditions.push(sql`${notification_logs.read_at} IS NOT NULL`);
    } else if (read === false) {
      whereConditions.push(sql`${notification_logs.read_at} IS NULL`);
    }

    if (from_date) {
      whereConditions.push(
        sql`${notification_logs.created_at} >= ${from_date}`,
      );
    }

    if (to_date) {
      whereConditions.push(sql`${notification_logs.created_at} <= ${to_date}`);
    }
    // Notification type filter
    if (notification_type_id) {
      whereConditions.push(
        eq(notification_logs.notification_type_id, notification_type_id),
      );
    }

    // Module filter
    if (module) {
      whereConditions.push(eq(notification_types.module, module));
    }

    if (channels && channels.length > 0) {
      whereConditions.push(
        sql`${notification_logs.channels} && ARRAY[${sql.join(channels, ', ')}]::text[]`,
      );
    }

    // Status filter
    if (status) {
      whereConditions.push(eq(notification_logs.status, status));
    }

    // Text search in title and body
    if (search && search.trim() !== '') {
      whereConditions.push(
        sql`(${notification_logs.title} ILIKE ${`%${search}%`} OR ${notification_logs.body} ILIKE ${`%${search}%`})`,
      );
    }

    const sortField = this.getSortField(notification_logs, sort, 'created_at');
    const sortOrder = order === 'desc' ? 'DESC' : 'ASC';

    try {
      // Get notifications
      const notifications = await this.drizzle.db
        .select({
          notification: notification_logs,
          notification_type: notification_types,
        })
        .from(notification_logs)
        .leftJoin(
          notification_types,
          eq(notification_logs.notification_type_id, notification_types.id),
        )
        .where(and(...whereConditions))
        .orderBy(
          sql`${notification_logs[sortField as keyof typeof notification_logs]} ${sql.raw(sortOrder)}`,
        )
        .limit(limit)
        .offset(offset);

      // Get total count
      const countResult = await this.drizzle.db
        .select({ count: sql<number>`count(*)` })
        .from(notification_logs)
        .leftJoin(
          notification_types,
          eq(notification_logs.notification_type_id, notification_types.id),
        )
        .where(and(...whereConditions));

      const result = {
        data: notifications.map((n) => ({
          ...n.notification,
        })),
        total: Number(countResult[0]?.count || 0),
        page,
        limit,
        pages: Math.ceil(Number(countResult[0]?.count || 0) / limit),
      };

      // Cache the result
      // Use a shorter TTL for mobile clients to ensure fresh data
      const ttl = channels?.includes('push')
        ? CACHE_TTL.FIVE_MINUTES
        : CACHE_TTL.TEN_MINUTES;

      await this.cacheService.set(cacheKey, result, ttl);

      return result;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.stack : String(error);
      this.logger.error(
        `Error fetching notification history for user ${userId}:`,
        errorMessage,
      );
      throw error;
    }
  }

  /**
   * Get user notification history with entity details
   * @param userId - The user ID to get notifications for
   * @param params - Query parameters for filtering and pagination
   * @param includeEntityDetails - Whether to include entity details
   * @returns Paginated notification history with entity details
   */
  async getUserNotificationHistoryWithEntityDetails(
    userId: string,
    params: NotificationHistoryQueryDto,
    includeEntityDetails: boolean = false,
  ): Promise<{
    data: NotificationLog[];
    total: number;
    page: number;
    limit: number;
    pages: number;
  }> {
    // Get the basic notification history
    const history = await this.getUserNotificationHistory(userId, params);

    if (!includeEntityDetails) {
      return history;
    }

    // Enrich notifications with entity details
    try {
      const enrichedData = await Promise.all(
        history.data.map(async (notification) => {
          try {
            return await this.notificationEntityService.enrichNotificationWithEntityDetails(
              notification,
            );
          } catch (error) {
            this.logger.warn(
              `Failed to enrich notification ${notification.id}:`,
              error,
            );
            return notification; // Return original if enrichment fails
          }
        }),
      );

      return {
        ...history,
        data: enrichedData,
      };
    } catch (error) {
      this.logger.error(
        'Error enriching notifications with entity details:',
        error,
      );
      // Return original data if enrichment fails
      return history;
    }
  }

  /**
   * Generate a cache key for notification history
   * @private
   */
  private async generateNotificationHistoryCacheKey(
    userId: string,
    params: NotificationHistoryQueryDto,
  ): Promise<string> {
    // Create a stable representation of the query parameters
    const queryString = JSON.stringify({
      page: params.page || 1,
      limit: params.limit || 10,
      read: params.read,
      from_date: params.from_date,
      to_date: params.to_date,
      notification_type_id: params.notification_type_id,
      channels: params.channels?.sort(), // Sort to ensure consistent order
      status: params.status,
      search: params.search,
      sort: params.sort || 'created_at',
      order: params.order || 'desc',
      module: params.module,
    });

    // Create a hash of the query string to keep the key length reasonable
    const queryHash = createHash('md5').update(queryString).digest('hex');

    // Check if there's an invalidation timestamp
    const invalidationKey = `${CACHE_PREFIXES.NOTIFICATION}:history:${userId}:last_updated`;
    const lastUpdated = (await this.cacheService.get(invalidationKey)) || '0';

    // Include the invalidation timestamp in the cache key to ensure we get fresh data after updates
    return `${CACHE_PREFIXES.NOTIFICATION}:history:${userId}:${queryHash}:${lastUpdated}`;
  }

  /**
   * Mark notification as read
   */
  async markNotificationAsRead(
    userId: string,
    notificationId: string,
  ): Promise<void> {
    try {
      await this.drizzle.db
        .update(notification_logs)
        .set({ read_at: new Date().toISOString() })
        .where(
          and(
            eq(notification_logs.id, notificationId),
            eq(notification_logs.user_id, userId),
          ),
        );

      // Invalidate all notification history cache for this user
      await this.invalidateNotificationHistoryCache(userId);

      // Also invalidate the specific notification log cache
      const logCacheKey = `${CACHE_PREFIXES.NOTIFICATION}:log:${userId}:${notificationId}`;
      await this.cacheService.del(logCacheKey);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.stack : String(error);
      this.logger.error(
        `Failed to mark notification ${notificationId} as read for user ${userId}:`,
        errorMessage,
      );
      throw error;
    }
  }

  /**
   * Invalidate notification history cache for a user
   * @private
   */
  private async invalidateNotificationHistoryCache(
    userId: string,
  ): Promise<void> {
    try {
      // Since we can't use pattern-based deletion directly, we'll use a special cache key
      // to track when the user's notifications were last updated
      const invalidationKey = `${CACHE_PREFIXES.NOTIFICATION}:history:${userId}:last_updated`;

      // Update the timestamp to invalidate all previous caches
      await this.cacheService.set(
        invalidationKey,
        Date.now().toString(),
        CACHE_TTL.ONE_DAY,
      );

      // Also invalidate the unread count cache
      const unreadCountKey = `${CACHE_PREFIXES.NOTIFICATION}:unread_count:${userId}`;
      await this.cacheService.del(unreadCountKey);
    } catch (error: unknown) {
      // Log error but don't throw - cache invalidation should not block the main operation
      const errorMessage = error instanceof Error ? error.stack : String(error);
      this.logger.error(
        `Failed to invalidate notification history cache for user ${userId}:`,
        errorMessage,
      );
    }
  }

  /**
   * Get notification log by ID
   * @param userId - The user ID
   * @param logId - The notification log ID
   * @returns The notification log
   * @throws NotFoundException if the log is not found
   */
  async getNotificationLogById(
    userId: string,
    logId: string,
  ): Promise<NotificationLog> {
    // Generate cache key
    const cacheKey = `${CACHE_PREFIXES.NOTIFICATION}:log:${userId}:${logId}`;

    // Try to get from cache first
    const cachedLog = await this.cacheService.get(cacheKey);
    if (cachedLog) {
      return cachedLog;
    }

    const result = await this.drizzle.db
      .select()
      .from(notification_logs)
      .where(
        and(
          eq(notification_logs.id, logId),
          eq(notification_logs.user_id, userId),
        ),
      )
      .then((results) => results[0]);

    if (!result) {
      throw new NotFoundException(
        `Notification log with ID ${logId} not found for user ${userId}`,
      );
    }

    // Cache the result for 10 minutes
    await this.cacheService.set(cacheKey, result, CACHE_TTL.TEN_MINUTES);

    return result;
  }

  /**
   * Perform comprehensive cache invalidation for notification logs
   * @param userId The user ID
   * @param logId The notification log ID
   * @param notificationType Optional notification type information for more targeted invalidation
   * @private
   */
  private async invalidateNotificationLogCaches(
    userId: string,
    logId: string,
    notificationType?: { id: string; module?: string },
  ): Promise<void> {
    try {
      const logCacheKey = `${CACHE_PREFIXES.NOTIFICATION}:log:${userId}:${logId}`;
      await this.cacheService.del(logCacheKey);

      await this.invalidateNotificationHistoryCache(userId);

      if (notificationType) {
        // Invalidate notification type specific caches
        if (notificationType.id) {
          // Invalidate history by type cache
          const typeHistoryKey = `${CACHE_PREFIXES.NOTIFICATION}:history:${userId}:type:${notificationType.id}`;
          await this.cacheService.del(typeHistoryKey);

          // Invalidate notifications by type cache (for all limits)
          try {
            const typePattern = `*:${CACHE_PREFIXES.NOTIFICATION}:type:${userId}:${notificationType.id}:*`;
            await this.cacheService.invalidatePattern(typePattern);
          } catch (patternError) {
            // This is expected to fail in some Redis configurations
            // Individual cache keys will be regenerated on next access
          }
        }

        // Invalidate module specific caches
        if (notificationType.module) {
          const moduleKey = `${CACHE_PREFIXES.NOTIFICATION}:history:${userId}:module:${notificationType.module}`;
          await this.cacheService.del(moduleKey);
        }
      }

      try {
        // This might not work in all Redis configurations
        const pattern = `*:${CACHE_PREFIXES.NOTIFICATION}:*:${userId}:*`;
        await this.cacheService.invalidatePattern(pattern);
      } catch (patternError) {
        // This is expected to fail in some Redis configurations, so just log it
      }
    } catch (error: unknown) {
      // Log error but don't throw - cache invalidation should not block the main operation
      const errorMessage = error instanceof Error ? error.stack : String(error);
      this.logger.error(
        `Failed to invalidate notification log caches for user ${userId}, log ${logId}:`,
        errorMessage,
      );
    }
  }

  /**
   * Mark all unread notifications as read for a user
   * @param userId - The user ID
   * @returns Promise that resolves when all notifications are marked as read
   */
  async markAllNotificationsAsRead(userId: string): Promise<void> {
    try {
      const now = new Date().toISOString();

      // Update all unread notifications in a single query
      await this.drizzle.db
        .update(notification_logs)
        .set({ read_at: now })
        .where(
          and(
            eq(notification_logs.user_id, userId),
            sql`${notification_logs.read_at} IS NULL`,
          ),
        );

      // Invalidate all notification history cache for this user
      await this.invalidateNotificationHistoryCache(userId);

      // Try to invalidate all user-specific notification caches
      try {
        const userPattern = `*:${CACHE_PREFIXES.NOTIFICATION}:*:${userId}:*`;
        await this.cacheService.invalidatePattern(userPattern);
      } catch (patternError) {
        // This is expected to fail in some Redis configurations
        // Fall back to incrementing the cache version
        await this.cacheService.incrementCacheVersion(
          CACHE_PREFIXES.NOTIFICATION,
        );
      }

      this.logger.log(`Marked all notifications as read for user ${userId}`);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.stack : String(error);
      this.logger.error(
        `Failed to mark all notifications as read for user ${userId}:`,
        errorMessage,
      );
      throw error;
    }
  }

  /**
   * Mark multiple notifications as read
   * @param userId - The user ID
   * @param notificationIds - Array of notification IDs to mark as read
   * @returns Promise that resolves when all notifications are marked as read
   */
  async markMultipleNotificationsAsRead(
    userId: string,
    notificationIds: string[],
  ): Promise<void> {
    if (!notificationIds.length) {
      return;
    }

    try {
      const now = new Date().toISOString();

      // Update all notifications in a single query
      await this.drizzle.db
        .update(notification_logs)
        .set({ read_at: now })
        .where(
          and(
            sql`${notification_logs.id} IN (${sql.join(notificationIds, ', ')})`,
            eq(notification_logs.user_id, userId),
          ),
        );

      // Invalidate all notification history cache for this user
      await this.invalidateNotificationHistoryCache(userId);

      // Also invalidate the specific notification log caches
      await Promise.all(
        notificationIds.map(async (notificationId) => {
          const logCacheKey = `${CACHE_PREFIXES.NOTIFICATION}:log:${userId}:${notificationId}`;
          await this.cacheService.del(logCacheKey);
        }),
      );

      this.logger.log(
        `Marked ${notificationIds.length} notifications as read for user ${userId}`,
      );
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.stack : String(error);
      this.logger.error(
        `Failed to mark multiple notifications as read for user ${userId}:`,
        errorMessage,
      );
      throw error;
    }
  }

  /**
   * Delete a notification log
   * @param userId The user ID
   * @param logId The notification log ID
   */
  async deleteNotificationLog(userId: string, logId: string): Promise<void> {
    try {
      // First, verify the notification exists and belongs to the user
      const notification = await this.getNotificationLogById(userId, logId);

      // Extract notification type info for targeted cache invalidation
      const notificationType = notification?.notification_type_id
        ? { id: notification.notification_type_id }
        : undefined;

      // Delete the notification log
      await this.drizzle.db
        .delete(notification_logs)
        .where(
          and(
            eq(notification_logs.id, logId),
            eq(notification_logs.user_id, userId),
          ),
        );

      // Perform comprehensive cache invalidation
      await this.invalidateNotificationLogCaches(
        userId,
        logId,
        notificationType,
      );

      this.logger.log(`Deleted notification log ${logId} for user ${userId}`);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.stack : String(error);
      this.logger.error(
        `Failed to delete notification log ${logId} for user ${userId}:`,
        errorMessage,
      );
      throw error;
    }
  }

  /**
   * Standardize notification data based on notification type
   * @param data - Original notification data
   * @param notificationType - Notification type information
   * @returns Standardized data with appropriate ID fields
   */
  private standardizeNotificationData(
    data: Record<string, unknown> | undefined,
    notificationType: any,
  ): Record<string, unknown> {
    if (!data || !notificationType) {
      return data || {};
    }

    const standardizedData = { ...data };
    const module = notificationType.module;

    // Ensure appropriate ID fields are present based on module type
    switch (module) {
      case 'event':
        // For event notifications, ensure event_id is present
        if (data.eventId && !data.event_id) {
          standardizedData.event_id = data.eventId;
        }
        // Also include post_id if available (events are posts)
        if (data.postId && !data.post_id) {
          standardizedData.post_id = data.postId;
        }
        break;

      case 'post':
        // For post notifications, ensure post_id is present
        if (data.postId && !data.post_id) {
          standardizedData.post_id = data.postId;
        }
        break;

      case 'opportunity':
        // For opportunity notifications, ensure opportunity_id is present
        if (data.opportunityId && !data.opportunity_id) {
          standardizedData.opportunity_id = data.opportunityId;
        }
        // Also include post_id if available (opportunities are posts)
        if (data.postId && !data.post_id) {
          standardizedData.post_id = data.postId;
        }
        break;

      case 'quiz':
        // For quiz notifications, ensure quiz_id is present
        if (data.quizId && !data.quiz_id) {
          standardizedData.quiz_id = data.quizId;
        }
        break;

      case 'raffle':
        // For raffle notifications, ensure raffle_id is present
        if (data.raffleId && !data.raffle_id) {
          standardizedData.raffle_id = data.raffleId;
        }
        break;

      default:
        // For other modules, no specific ID standardization needed
        break;
    }

    return standardizedData;
  }

  /**
   * Log notification
   * @param params - Notification parameters
   * @returns Promise that resolves when the notification is logged
   */
  async logNotification(params: {
    notificationTypeId: string;
    userId?: string;
    title: string;
    body: string;
    data?: Record<string, unknown>;
    channels: string[];
    status: string;
    error?: string;
  }): Promise<NotificationLog | null> {
    const {
      notificationTypeId,
      userId,
      title,
      body,
      data,
      channels,
      status,
      error,
    } = params;

    try {
      // Skip logging if userId is not provided
      if (!userId) {
        this.logger.warn(
          'No userId provided for notification logging. Skipping.',
        );
        return null;
      }

      // Check if user exists before logging
      const [userExists] = await this.drizzle.db
        .select({ id: users.id })
        .from(users)
        .where(eq(users.id, userId));

      if (!userExists) {
        this.logger.warn(
          `User ${userId} does not exist. Skipping notification logging.`,
        );
        return null;
      }

      // Get notification type information for data standardization
      const [notificationType] = await this.drizzle.db
        .select()
        .from(notification_types)
        .where(eq(notification_types.id, notificationTypeId));

      // Standardize the data based on notification type
      const standardizedData = this.standardizeNotificationData(
        data,
        notificationType,
      );

      // Insert notification log
      const [newLog] = await this.drizzle.db
        .insert(notification_logs)
        .values({
          notification_type_id: notificationTypeId,
          user_id: userId,
          title,
          body,
          data: standardizedData,
          channels,
          status,
          error,
        })
        .returning();

      // Invalidate notification history cache for this user
      // This ensures that when a new notification is created, the user will see it immediately
      await this.invalidateNotificationHistoryCache(userId);

      // If we have the new log ID, we can pre-cache it to improve performance
      if (newLog && newLog.id) {
        const logCacheKey = `${CACHE_PREFIXES.NOTIFICATION}:log:${userId}:${newLog.id}`;
        await this.cacheService.set(logCacheKey, newLog, CACHE_TTL.TEN_MINUTES);
        return newLog;
      }

      return null;
    } catch (logError: unknown) {
      const errorMessage =
        logError instanceof Error ? logError.stack : String(logError);
      this.logger.error(`Failed to log notification:`, errorMessage);
      // Don't throw here to prevent cascading errors
      return null;
    }
  }

  /**
   * Clean up old notification logs
   */
  async cleanupOldNotificationLogs(daysToKeep: number = 30): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      // Get the list of affected users before deleting
      const affectedUsers = await this.drizzle.db
        .select({ user_id: notification_logs.user_id })
        .from(notification_logs)
        .where(
          sql`${notification_logs.created_at} < ${cutoffDate.toISOString()}`,
        )
        .groupBy(notification_logs.user_id);

      // Delete old logs
      await this.drizzle.db
        .delete(notification_logs)
        .where(
          sql`${notification_logs.created_at} < ${cutoffDate.toISOString()}`,
        );

      // Invalidate cache for all affected users
      const userIds = affectedUsers.map((u) => u.user_id);

      // Perform comprehensive cache invalidation for each affected user
      for (const userId of userIds) {
        if (userId) {
          // Invalidate notification history cache
          await this.invalidateNotificationHistoryCache(userId);

          // Try to invalidate all user-specific notification caches
          try {
            const userPattern = `*:${CACHE_PREFIXES.NOTIFICATION}:*:${userId}:*`;
            await this.cacheService.invalidatePattern(userPattern);
          } catch (patternError) {
            // This is expected to fail in some Redis configurations
          }
        }
      }

      // Try to invalidate all notification log caches with a pattern
      try {
        // Global notification cache invalidation
        await this.cacheService.invalidatePattern(
          `*:${CACHE_PREFIXES.NOTIFICATION}:log:*`,
        );

        // Also try to invalidate history caches
        await this.cacheService.invalidatePattern(
          `*:${CACHE_PREFIXES.NOTIFICATION}:history:*`,
        );
      } catch (cacheError) {
        // If pattern-based invalidation fails, update the global cache version
        // This is a more aggressive approach but ensures cache consistency
        try {
          await this.cacheService.incrementCacheVersion(
            CACHE_PREFIXES.NOTIFICATION,
          );
        } catch (versionError) {
          this.logger.error('Failed to increment cache version', versionError);
        }
      }

      this.logger.log(
        `Cleaned up notification logs older than ${daysToKeep} days for ${userIds.length} users`,
      );
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.stack : String(error);
      this.logger.error(
        `Failed to clean up old notification logs:`,
        errorMessage,
      );
      throw error;
    }
  }

  /**
   * Get notifications by type for a user
   * @param userId - The user ID
   * @param notificationTypeId - The notification type ID
   * @param limit - Maximum number of notifications to return
   * @returns Array of notifications of the specified type
   */
  async getNotificationsByType(
    userId: string,
    notificationTypeId: string,
    limit: number = 10,
  ): Promise<NotificationLog[]> {
    // Generate cache key
    const cacheKey = `${CACHE_PREFIXES.NOTIFICATION}:type:${userId}:${notificationTypeId}:${limit}`;

    // Try to get from cache first
    const cachedNotifications =
      await this.cacheService.get<NotificationLog[]>(cacheKey);
    if (cachedNotifications) {
      return cachedNotifications;
    }

    try {
      // Get notifications of the specified type
      const notifications = await this.drizzle.db
        .select()
        .from(notification_logs)
        .where(
          and(
            eq(notification_logs.user_id, userId),
            eq(notification_logs.notification_type_id, notificationTypeId),
          ),
        )
        .orderBy(sql`${notification_logs.created_at} DESC`)
        .limit(limit);

      // Cache the result for 5 minutes
      await this.cacheService.set(
        cacheKey,
        notifications,
        CACHE_TTL.FIVE_MINUTES,
      );

      return notifications;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.stack : String(error);
      this.logger.error(
        `Failed to get notifications by type for user ${userId} and type ${notificationTypeId}:`,
        errorMessage,
      );
      return [];
    }
  }

  /**
   * Get unread notification count for a user
   * @param userId - The user ID
   * @returns The number of unread notifications
   */
  async getUnreadNotificationCount(userId: string): Promise<number> {
    // Generate cache key
    const cacheKey = `${CACHE_PREFIXES.NOTIFICATION}:unread_count:${userId}`;

    // Try to get from cache first
    const cachedCount = await this.cacheService.get<number>(cacheKey);
    if (cachedCount !== null) {
      return cachedCount;
    }

    try {
      // Get count of unread notifications
      const [result] = await this.drizzle.db
        .select({ count: sql<number>`count(*)` })
        .from(notification_logs)
        .where(
          and(
            eq(notification_logs.user_id, userId),
            sql`${notification_logs.read_at} IS NULL`,
          ),
        );

      const count = Number(result?.count || 0);

      // Cache the result for 5 minutes
      await this.cacheService.set(cacheKey, count, CACHE_TTL.FIVE_MINUTES);

      return count;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.stack : String(error);
      this.logger.error(
        `Failed to get unread notification count for user ${userId}:`,
        errorMessage,
      );
      return 0;
    }
  }

  /**
   * Get a valid sort field name or fall back to default
   * @param entity - The database entity to check fields against
   * @param sortField - The requested sort field
   * @param defaultField - The default field to fall back to
   * @returns A valid sort field name
   */
  private getSortField(
    entity: any,
    sortField: string | undefined,
    defaultField: string = 'created_at',
  ): string {
    // List of valid sort fields for this entity
    const validFields = Object.keys(entity);

    // Check if the requested sort field is valid
    if (sortField && validFields.includes(sortField)) {
      return sortField;
    }

    // Fall back to default field
    return defaultField;
  }
}
