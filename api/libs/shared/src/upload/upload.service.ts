import { Injectable, Logger } from '@nestjs/common';
import { DrizzleService } from '../drizzle/drizzle.service';
import { post_statuses, postImages, posts, PostStatus } from '@/db/schema';
import { eq } from 'drizzle-orm';

@Injectable()
export class UploadQueueService {
  private readonly logger = new Logger(UploadQueueService.name);

  constructor(private readonly drizzle: DrizzleService) {}

  async updatePostImages({
    imageUrl,
    postId,
    isLastImage = false,
    status,
  }: {
    imageUrl: string;
    postId: string;
    isLastImage?: boolean;
    status?: PostStatus;
  }) {
    try {
      const existingPost = await this.drizzle.db.query.posts.findFirst({
        where: eq(posts.id, postId),
      });
      if (!existingPost) {
        this.logger.error(`Post with ID ${postId} not found`);
        throw new Error(`Post with ID ${postId} not found`);
      }

      const postImage = await this.drizzle.db.insert(postImages).values({
        postId,
        imageUrl,
      });

      if (isLastImage) {
        await this.drizzle.db
          .update(posts)
          .set({ status: status || post_statuses.ACTIVE })
          .where(eq(posts.id, postId))
          .returning();
      }
      this.logger.debug(
        `Updated post images for post ${postId} with image ${imageUrl}`,
      );
      return postImage;
    } catch (error: any) {
      this.logger.error(
        `Error updating post images for post ${postId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
