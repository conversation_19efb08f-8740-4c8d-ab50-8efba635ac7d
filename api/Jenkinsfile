/* groovylint-disable DuplicateStringLiteral, VariableTypeRequired */
@Library('shared-libraries')

def appName = 'touching-lives-backend'

def deployConfig = [
    dev:[
        revisionTag: appName,
        revisionLocation: 'reach-deployment-asset',
        assetsPath: 'app/',
        codeDeployAppName: 'touching-lives',
        codeDeployGroup: appName
    ],
        
     'staging': [
        revisionTag: "${appName}-staging",
        revisionLocation: 'reach-deployment-asset',
        assetsPath: 'app/',
        codeDeployAppName: 'touching-lives',
        codeDeployGroup: "${appName}-staging" 
    ],
      'main':[
        revisionTag: "${appName}-prod",
        revisionLocation: 'reach-deployment-asset-prod',
        assetsPath: 'app/',
        codeDeployAppName: 'touching-lives',
        codeDeployGroup: "reach-backend-prod"    
    ]
]

def awsCreds = [
    region: 'eu-west-1',
    iamCredId: 'aws-reach-cred'
]


def sharedBranches = ['dev', 'staging', 'main']

pipeline {
    agent any

    tools{
        nodejs 'node20'
    }

    environment {
        currentBranch = "${env.BRANCH_NAME}"
        gitUser = sh(script: 'git log -1 --pretty=format:%ae', returnStdout: true).trim()
        gitSha = sh(script: 'git log -n 1 --pretty=format:"%H"', returnStdout: true).trim()
        imageRegistry = '345594579871.dkr.ecr.eu-west-1.amazonaws.com'
    }

    stages {
                 stage('Set Image Name') {
            when {
                expression { return sharedBranches.contains(env.BRANCH_NAME) }
            }
            steps {
                script {
                    // Dynamically set image name based on branch

                    if (currentBranch == 'staging') {
                        env.imageName = 'touching-lives-backend-staging'
                    } else if (currentBranch == 'main') {
                        env.imageName = 'touching-lives-backend-prod'
                    } else {
                        env.imageName = 'touching-lives-backend'
                    }
                    env.imageTag = "${imageRegistry}/${env.imageName}:${gitSha}"
                    env.imageTagLatest = "${imageRegistry}/${env.imageName}:latest"
                }
            }
        }
        stage('SonarQube Analysis') {
            when {
                 expression { return sharedBranches.contains(env.BRANCH_NAME) }
            }
                    steps {
                        dir('api'){
                    sonarQubeAnalysis()
                        }
                    }
        }

        stage('Build Application Image') {
      when {
                expression { return sharedBranches.contains(env.BRANCH_NAME) }
            }
            steps {
                dir('api') {
                    script {  
                        buildDockerImage(imageTag: imageTag, buildContext: './')
                        sh "docker tag ${imageTag} ${imageTagLatest}"
                    }
                }
            }
        }
               
   

        stage('Push to Registry') {
        when {
            anyOf{
                     branch 'dev';
                     branch 'staging';
                 } 
            }
            steps {
                dir('api') {
                    script {
                        pushDockerImage(image: imageTag, registry: imageRegistry, awsCreds: awsCreds)
                        pushDockerImage(image: imageTagLatest, registry: imageRegistry, awsCreds: awsCreds)
                    }
                }
            }
        }

 stage('Update Compose File') {
      when {
            anyOf{
                     branch 'dev';
                     branch 'staging';
                 } 
            }
    steps {
        dir('api') {
            script {
                // Set the image name and port based on the branch  
                def port = '3003' 
                def containerName = 'touching-lives-backend'
                def imageName1 = 'touching-lives-backend'
                def destination = 'touching-lives'
                if (env.BRANCH_NAME == 'staging') {
                    port = '4003' 
                    containerName = 'touching-lives-backend-staging'
                    imageName1 = 'touching-lives-backend-staging'
                    destination = 'touching-lives-backend-staging'
                }

                // Make the script executable and run it with the correct image name, tag, and port
                sh 'chmod +x scripts/update-image-tag.sh'
                sh """./scripts/update-image-tag.sh ${imageName1} ${gitSha} ${port} ${containerName} ${destination}"""
            }
        }
    }
}

        stage('Prepare to Deploy') {
      when {
            anyOf{
                     branch 'dev';
                     branch 'staging';
                 } 
            }
            steps {
                dir('api') {
                    script {
                        sh 'mkdir -p app/'
                        sh 'cp docker-compose.yml app/'
                        sh 'cp scripts -r app/'
                        sh 'cp appspec.yml app/'
                        prepareToDeployECR(environment: currentBranch, deploymentConfig: deployConfig, awsCreds: awsCreds)
                    }
                }
            }
        }

        stage('Deploy') {
      when {
            anyOf{
                     branch 'dev';
                     branch 'staging';
                 } 
            }
            steps {
                dir('api') {
                    script {
                        makeDeploymentECR(environment: currentBranch, deploymentConfig: deployConfig, awsCreds: awsCreds)
                    }
                }
            }
        }


                stage('Push to Production') {
            when {
                expression { return currentBranch == 'main' }
            }
            steps {
                dir('api') {
                    script {

                        // Push the production image to the registry
                        pushDockerImage(image: imageTag, registry: imageRegistry, awsCreds: awsCreds)
                        pushDockerImage(image: imageTagLatest, registry: imageRegistry, awsCreds: awsCreds)
                    }
                }
            }
        }
        
                stage('Clean Up Build') {
            when {
                expression { return sharedBranches.contains(env.BRANCH_NAME)}
            }
            steps {
                script {
                    sh """
                        docker rmi ${imageTag} || true
                        docker rmi ${imageTagLatest} || true
                        docker system prune -af --volumes || true
                    """
                }    
            }
        }

        stage('CleanUp WS') {
            steps {
                script {
                    // Delete workspace contents
                    sh """
                        rm -rf .* || true
                        rm -rf * || true
                    """
                    // Use Jenkins cleanWs with additional parameters
                    cleanWs(
                        cleanWhenSuccess: true,
                        cleanWhenFailure: true,
                        cleanWhenUnstable: true,
                        deleteDirs: true,
                        disableDeferredWipeout: true,
                        notFailBuild: true
                    )
                }
            }
        }
    }
}

