import {
  Body,
  Controller,
  Logger,
  Param,
  Post,
  Get,
  Delete,
  UseGuards,
  Put,
  Query,
  Req,
  BadRequestException,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiOkResponse,
} from '@nestjs/swagger';
import { StudentProfileService } from './student_profile.service';
import * as student_profileDto from './student_profile.dto';
import { ZodSerializerDto } from 'nestjs-zod';
import { UseRoles } from 'nest-access-control';
import { RoleGuard } from '@/guards/role.guard';
import { insertStudentProfileSchema } from '@/db/schema/student_profile';
import { type User as UserDecoratorType } from '@/db/schema';
import { User } from '@/guards/user.decorator';
import { CLIENT_TYPE } from '@/guards/request-validation.decorator';

import { AppClients } from '@app/shared/constants/auth.constants';
import { StudentProfileRoutes } from '@app/shared/constants/student_profile.constants';
import { PointSystemService } from '@/point-system/point_system.service';
import { QuizService } from '@/mcq/quiz/quiz.service';
import { periodQueryDto, queryParamsDto } from '@/common/dto/query-params.dto';
import { CustomParseUUIDPipe } from '@/common/pipes/custom-parse-uuid';
import type { Request } from 'express';
import { SkillsService } from '@/skills/skills.service';

@Controller({ version: '1', path: 'student-profile' })
@ApiTags('Student-profile')
export class StudentProfileController {
  constructor(
    private readonly student_profile: StudentProfileService,
    private readonly point_system: PointSystemService,
    private readonly quizService: QuizService,
    private readonly skillService: SkillsService,
  ) {}
  private readonly logger = new Logger(StudentProfileController.name);

  /**
   * Count student profiles
   */
  @Get(StudentProfileRoutes.COUNT)
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'profile',
    action: 'read',
    possession: 'any',
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  @ApiOperation({
    summary: 'Count student profiles',
    description: 'Counts student profiles with optional filtering.',
  })
  @ApiQuery({
    name: 'institutionId',
    required: false,
    description: 'Institution IDs (comma-separated)',
    type: String,
  })
  @ApiQuery({
    name: 'programme',
    required: false,
    description: 'Programmes (comma-separated)',
    type: String,
  })
  @ApiOkResponse({
    description: 'Profile count retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        count: { type: 'number' },
        students: { type: 'array', items: { type: 'object' } },
      },
    },
  })
  @ApiBadRequestResponse({ description: 'Invalid parameters' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiForbiddenResponse({ description: 'Forbidden' })
  async countStudentProfiles(
    @Query() data: student_profileDto.StudentProfileCountQueryParam,
  ) {
    try {
      const transformedData = this.transformData(data);
      const { institutionId, programme, level, degree } = transformedData;

      const { total, students } = await this.student_profile.getAllStudents(
        institutionId,
        programme,
        level,
        degree,
      );
      return { count: total, students };
    } catch (error: any) {
      this.logger.error('Error counting student profiles', error.stack);
      throw error;
    }
  }
  private transformData = ({
    institutionId,
    programme,
    level,
    degree,
  }: student_profileDto.StudentProfileCountQueryParam) => ({
    institutionId: institutionId?.split(',').map(String) ?? [],
    programme: programme?.split(',') ?? [],
    level: level?.split(',').map(Number) ?? [],
    degree: degree?.split(',') ?? [],
  });

  /**
   * Create a new student profile
   */
  @Post()
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'profile',
    action: 'create',
    possession: 'own',
  })
  @ZodSerializerDto(insertStudentProfileSchema)
  @ApiOperation({
    summary: 'Create a new student profile',
    description: 'Creates a new student profile for the authenticated user.',
  })
  @ApiBody({
    type: student_profileDto.StudentProfileDto,
    description: 'Student profile data',
  })
  @ApiCreatedResponse({
    status: 201,
    description: 'Profile created successfully',
    type: student_profileDto.StudentProfileDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiConflictResponse({
    description: 'Profile already exists or username in use',
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiForbiddenResponse({ description: 'Forbidden' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.MOBILE, AppClients.WEB)
  async addStudentProfile(
    @Body() studentProfileInput: student_profileDto.StudentProfileDto,
    @User(['student', 'student_admin']) user: UserDecoratorType,
  ) {
    try {
      return await this.student_profile.addStudentProfile(
        studentProfileInput,
        user,
      );
    } catch (error: any) {
      this.logger.error('Error adding student profile', error);
      throw error;
    }
  }

  /**
   * Get student points statistics
   */
  @Get(StudentProfileRoutes.GET_STUDENT_POINTS)
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'profile',
    action: 'read',
    possession: 'own',
  })
  @ApiBearerAuth()
  @ZodSerializerDto(student_profileDto.StudentProfileDto)
  @ApiOperation({
    summary: 'Get student points statistics',
    description: 'Retrieves point statistics for a student profile.',
  })
  @ApiParam({
    name: 'id',
    description: 'Student profile ID',
    type: String,
    format: 'uuid',
    required: true,
  })
  @ApiQuery({
    name: 'period',
    description: 'Time period (day, week, month, year, all)',
    required: false,
    enum: ['day', 'week', 'month', 'year', 'all'],
    type: String,
  })
  @ApiOkResponse({ description: 'Points statistics retrieved successfully' })
  @ApiBadRequestResponse({ description: 'Invalid parameters' })
  @ApiNotFoundResponse({ description: 'Profile not found' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiForbiddenResponse({ description: 'Forbidden' })
  @CLIENT_TYPE(AppClients.WEB)
  async getStudentPoints(
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @Query() query: periodQueryDto,
  ) {
    try {
      return await this.point_system.getStudentPoints(id, query);
    } catch (error: any) {
      this.logger.error('Error getting student points', error.stack);
      throw error;
    }
  }

  /**
   * Get student quiz activities
   */
  @Get(StudentProfileRoutes.GET_STUDENT_QUIZ_ACTIVITIES)
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'profile',
    action: 'read',
    possession: 'own',
  })
  @ApiBearerAuth()
  @ZodSerializerDto(student_profileDto.StudentProfileDto)
  @ApiOperation({
    summary: 'Get student quiz activities',
    description: 'Retrieves quiz activity statistics for a student profile.',
  })
  @ApiParam({
    name: 'id',
    description: 'Student profile ID',
    type: String,
    format: 'uuid',
    required: true,
  })
  @ApiQuery({
    name: 'period',
    description: 'Time period (day, week, month, year, all)',
    required: false,
    enum: ['day', 'week', 'month', 'year', 'all'],
    type: String,
  })
  @ApiOkResponse({ description: 'Quiz activities retrieved successfully' })
  @ApiBadRequestResponse({ description: 'Invalid parameters' })
  @ApiNotFoundResponse({ description: 'Profile not found' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiForbiddenResponse({ description: 'Forbidden' })
  @CLIENT_TYPE(AppClients.WEB)
  async getStudentQuizActivities(
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @Query() query: periodQueryDto,
  ) {
    try {
      return await this.quizService.getStudentQuizActivities(id, query);
    } catch (error: any) {
      this.logger.error('Error getting student quiz activities', error.stack);
      throw error;
    }
  }

  /**
   * Get student quiz history
   */
  @Get(StudentProfileRoutes.GET_STUDENT_QUIZ_HISTORY)
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'profile',
    action: 'read',
    possession: 'own',
  })
  @ApiBearerAuth()
  @ZodSerializerDto(student_profileDto.StudentProfileDto)
  @ApiOperation({
    summary: 'Get student quiz history',
    description:
      'Retrieves quiz history for a student profile with pagination.',
  })
  @ApiParam({
    name: 'id',
    description: 'Student profile ID',
    type: String,
    format: 'uuid',
    required: true,
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Items per page',
    required: false,
    type: Number,
  })
  @ApiOkResponse({ description: 'Quiz history retrieved successfully' })
  @ApiBadRequestResponse({ description: 'Invalid parameters' })
  @ApiNotFoundResponse({ description: 'Profile not found' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiForbiddenResponse({ description: 'Forbidden' })
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async getStudentQuizHistory(
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @Query() query: queryParamsDto,
  ) {
    try {
      return await this.quizService.getStudentQuizHistory(id, query);
    } catch (error: any) {
      this.logger.error('Error getting student quiz history', error.stack);
      throw error;
    }
  }

  @Get(StudentProfileRoutes.GET_STUDENT_SKILLS)
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'profile',
    action: 'read',
    possession: 'own',
  })
  @ApiBearerAuth()
  @ZodSerializerDto(student_profileDto.StudentProfileDto)
  @ApiOperation({
    summary: 'Get student skills',
    description: 'Retrieves skills associated with a student profile.',
  })
  @ApiParam({
    name: 'id',
    description: 'Student profile ID',
    type: String,
    format: 'uuid',
    required: true,
  })
  @ApiOkResponse({
    description: 'Student skills retrieved successfully',
    type: student_profileDto.StudentProfileDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid parameters' })
  @ApiNotFoundResponse({ description: 'Profile not found' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiForbiddenResponse({ description: 'Forbidden' })
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async getStudentSkills(
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @User() user: UserDecoratorType,
  ) {
    this.logger.log(`Fetching skills for student with id ${id}`);
    try {
      if (user.student_profile && user.student_profile.id !== id) {
        this.logger.log(
          `User is requesting their own skills for student with id ${id} but is not the owner  `,
        );
        throw new BadRequestException(
          "Students cannot access other students' skills",
        );
      }
      return await this.skillService.getStudentSkills(id);
    } catch (error: any) {
      this.logger.error(
        `Error fetching skills for student with id ${id}`,
        error.stack,
      );
      throw error;
    }
  }

  @Get(StudentProfileRoutes.GET_STUDENT_BY_USERNAME)
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'profile',
    action: 'read',
    possession: 'any',
  })
  @ApiBadRequestResponse({ description: 'Invalid parameters' })
  @ApiNotFoundResponse({ description: 'Profile not found' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiForbiddenResponse({ description: 'Forbidden' })
  /**
   * Get a student profile by ID
   */
  async getStudentProfileByUsername(
    @Param('username') username: string,
    @User() user: UserDecoratorType,
  ) {
    try {
      return await this.student_profile.getStudentProfileByUsername(
        username,
        user,
      );
    } catch (error: any) {
      this.logger.error(
        'Error getting student profile by username',
        error.stack,
      );
      throw error;
    }
  }

  @Get(StudentProfileRoutes.GET_BY_ID)
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'profile',
    action: 'read',
    possession: 'own',
  })
  @ApiBearerAuth()
  @ZodSerializerDto(student_profileDto.StudentProfileDto)
  @ApiOperation({
    summary: 'Get a student profile by ID',
    description: 'Retrieves a student profile by ID.',
  })
  @ApiParam({
    name: 'id',
    description: 'Student profile ID',
    type: String,
    format: 'uuid',
    required: true,
  })
  @ApiOkResponse({
    description: 'Student profile retrieved successfully',
    type: student_profileDto.StudentProfileDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid ID' })
  @ApiNotFoundResponse({ description: 'Profile not found' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiForbiddenResponse({ description: 'Forbidden' })
  @CLIENT_TYPE(AppClients.WEB)
  async getStudentProfile(
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @User() user: UserDecoratorType,
  ) {
    try {
      return await this.student_profile.getStudentProfileById(id, user);
    } catch (error: any) {
      this.logger.error('Error getting student profile', error.stack);
      throw error;
    }
  }

  /**
   * Get all student profiles
   */
  @Get()
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'profile',
    action: 'read',
    possession: 'any',
  })
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get all student profiles',
    description:
      'Retrieves a paginated list of student profiles with filtering options.',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'search',
    description: 'Search by name',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'sort',
    description: 'Field to sort by',
    required: false,
    enum: [
      'id',
      'first_name',
      'last_name',
      'institution',
      'level',
      'programme',
      'degree',
    ],
    type: String,
  })
  @ApiOkResponse({
    description: 'Student profiles retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/StudentProfileDto' },
        },
        meta: {
          type: 'object',
          properties: {
            page: { type: 'number' },
            limit: { type: 'number' },
            total: { type: 'number' },
            totalPages: { type: 'number' },
          },
        },
      },
    },
  })
  @ApiBadRequestResponse({ description: 'Invalid parameters' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiForbiddenResponse({ description: 'Forbidden' })
  @ZodSerializerDto(student_profileDto.StudentProfileDto)
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async getAllStudentProfiles(
    @Query() query: student_profileDto.StudentProfileQueryParamsDto,
    @User() user: UserDecoratorType,
    @Req() req: Request,
  ) {
    try {
      if (req.headers['x-client-type'] === AppClients.MOBILE) {
        if (!user.student_profile) {
          throw new BadRequestException(
            'Student profile not found for the user',
          );
        }

        return await this.student_profile.getStudentProfileById(
          user.student_profile?.id,
          user,
        );
      }

      return await this.student_profile.getStudentProfiles(query);
    } catch (error: any) {
      this.logger.error('Error getting all student profiles', error.stack);
      throw error;
    }
  }

  /**
   * Update a student profile
   */
  @Put(StudentProfileRoutes.UPDATE_BY_ID)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'profile',
    action: 'update',
    possession: 'own',
  })
  @ZodSerializerDto(student_profileDto.StudentProfileDto)
  @ApiOperation({
    summary: 'Update a student profile',
    description: 'Updates an existing student profile.',
  })
  @ApiParam({
    name: 'id',
    description: 'Student profile ID',
    type: String,
    format: 'uuid',
    required: true,
  })
  @ApiBody({
    type: student_profileDto.StudentProfileDto,
    description: 'Updated profile data',
  })
  @ApiOkResponse({
    description: 'Profile updated successfully',
    type: student_profileDto.StudentProfileDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({ description: 'Profile not found' })
  @ApiConflictResponse({ description: 'Username in use' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiForbiddenResponse({ description: 'Forbidden' })
  @CLIENT_TYPE(AppClients.MOBILE, AppClients.WEB)
  async updateStudentProfile(
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @Body() studentProfileInput: student_profileDto.StudentProfileDto,
    @User() user: UserDecoratorType,
  ) {
    try {
      return await this.student_profile.updateStudentProfile(
        id,
        studentProfileInput,
        user.id,
      );
    } catch (error: any) {
      this.logger.error('Error updating student profile', error.stack);
      throw error;
    }
  }

  /**
   * Delete a student profile
   */
  @Delete(StudentProfileRoutes.DELETE_BY_ID)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'profile',
    action: 'delete',
    possession: 'any',
  })
  @ApiOperation({
    summary: 'Delete a student profile',
    description: 'Soft deletes a student profile.',
  })
  @ApiParam({
    name: 'id',
    description: 'Student profile ID',
    type: String,
    format: 'uuid',
    required: true,
  })
  @ApiOkResponse({
    description: 'Profile deleted successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        id: { type: 'string', format: 'uuid' },
      },
    },
  })
  @ApiBadRequestResponse({ description: 'Invalid ID' })
  @ApiNotFoundResponse({ description: 'Profile not found' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiForbiddenResponse({ description: 'Forbidden' })
  @CLIENT_TYPE(AppClients.WEB)
  @ZodSerializerDto(student_profileDto.StudentProfileDto)
  async removeStudentProfile(
    @Param('id', new CustomParseUUIDPipe()) id: string,
  ) {
    try {
      return await this.student_profile.removeStudentProfile(id);
    } catch (error: any) {
      this.logger.error('Error removing student profile', error.stack);
      throw error;
    }
  }
}
