import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { DatabaseModule } from '@app/shared/drizzle/drizzle.module';
import { SharedModule } from '@app/shared';
import { EnvConfig } from '@app/shared/dto/env-config.dto';
import { EmailModule } from './mail/email.module';
import { AuthModule } from './auth/auth.module';
import { BullBoardAuthMiddleware } from './middleware/bull-board-auth.middleware';
import { CacheModule } from './cache/cache.module';
import { CacheExampleModule } from './examples/cache-example.module';
import { OrganisationController } from './organisation/organisation.controller';
import { OrganisationModule } from './organisation/organisation.module';
import { SeedModule } from './seed/seed.module';
import { TypedConfigModule } from 'nest-typed-config';
import { RBAC_ROLES } from './auth/app.roles';
import { AccessControlModule } from 'nest-access-control';
import { JwtHelperModule } from './jwt-helper/jwt-helper.module';
import { StudentProfileModule } from './student_profile/student_profile.module';
import { SkillsModule } from './skills/skills.module';
import { ClubModule } from './club/club.module';
import { InstitutionModule } from './institution/institution.module';
import { CountryModule } from './country/country.module';
import { AuthGuard } from './guards/auth.guard';
import { RepositoriesModule } from './repositories/repositories.module';
import { APP_GUARD } from '@nestjs/core';
import { PostModule } from './posts/post.module';
import { ClientTypeGuard } from './guards/request-validation.guard';
import { UploadModule } from './upload/upload.module';
import { ThrottlerModule, ThrottlerGuard } from '@nestjs/throttler';
import { McqModule } from './mcq/mcq.module';
import { ScheduleModule } from '@nestjs/schedule';
import { RaffleModule } from './raffle/raffle.module';
import { NotificationModule } from '../libs/shared/src/notification/notification.module';

import { EnhancedNotificationQueueModule } from '../libs/shared/src/enhanced-notification-queue/enhanced-notification-queue.module';
import { EventNotificationModule } from './event-notification/event-notification.module';
import { FirebaseModule } from './firebase/firebase.module';
import { ConfigModule } from '@nestjs/config';
import { PointSystemModule } from './point-system/point_system.module';
import { RedisModule } from '@app/shared/redis/redis.module';
import { RedisOptions } from '@app/shared/redis/redis.module-definition';
import { QueueModule } from '../libs/shared/src/queue/queue.module';
import { BullBoardUIModule } from '../libs/shared/src/queue/bull-board.module';
import { InAppQueueModule } from '../libs/shared/src/queue/in-app-queue.module';
import { NotificationQueueModule } from '../libs/shared/src/queue/notification-queue.module';

@Module({
  imports: [
    SharedModule,
    ScheduleModule.forRoot(),
    SharedModule.registerZodValidationPipe(),
    SharedModule.registerPinoLogger(),

    ThrottlerModule.forRootAsync({
      imports: [TypedConfigModule],
      inject: [EnvConfig],
      useFactory: (envConfig: EnvConfig) => [
        {
          ttl: envConfig.THROTTLE_TTL,
          limit: envConfig.THROTTLE_LIMIT,
        },
      ],
    }),
    DatabaseModule.registerAsync({
      inject: [EnvConfig],
      imports: [TypedConfigModule],
      useFactory: (envConfig: EnvConfig) => {
        return { connectionString: envConfig.DATABASE_URL };
      },
    }),
    EmailModule,
    AuthModule,
    OrganisationModule,
    SeedModule,
    AccessControlModule.forRoles(RBAC_ROLES, {}),
    JwtHelperModule,
    StudentProfileModule,
    SkillsModule,
    ClubModule,
    InstitutionModule,
    CountryModule,
    RepositoriesModule,
    PostModule,
    UploadModule,
    McqModule,
    RaffleModule,
    ConfigModule.forRoot({ isGlobal: true }),
    FirebaseModule,
    NotificationModule,
    EnhancedNotificationQueueModule,
    EventNotificationModule,
    PointSystemModule,
    QueueModule.register(),
    InAppQueueModule,
    NotificationQueueModule,
    BullBoardUIModule,
    // Redis module must be registered before cache module
    RedisModule.registerAsync({
      inject: [EnvConfig],
      imports: [TypedConfigModule],
      useFactory: async (envConfig: EnvConfig): Promise<RedisOptions> => {
        const mode = (envConfig.REDIS_MODE || 'single') as
          | 'single'
          | 'cluster'
          | 'auto';

        const config: RedisOptions = {
          mode,
          healthCheckInterval: envConfig.REDIS_HEALTHCHECK_INTERVAL_MS,
          fallbackToSingle: true,
        };

        // Always create single configuration for fallback
        config.single = {
          host: envConfig.REDIS_HOST,
          port: envConfig.REDIS_PORT,
          password: envConfig.REDIS_PASSWORD,
          username: envConfig.REDIS_USERNAME,
          db: envConfig.REDIS_DB,
        };

        // Only add TLS if explicitly enabled
        if (envConfig.REDIS_TLS === true) {
          config.single.tls = {};
        }

        // Create cluster configuration if needed
        if (mode === 'cluster' || mode === 'auto') {
          if (envConfig.REDIS_CLUSTER_NODES) {
            const clusterNodes = envConfig.REDIS_CLUSTER_NODES.split(',')
              .map((node) => {
                const [host, port] = node.trim().split(':');
                return {
                  host: host || 'localhost',
                  port: parseInt(port || '6379', 10),
                };
              })
              .filter((node) => node.host && node.port);

            if (clusterNodes.length > 0) {
              const clusterRedisOptions: any = {
                password: envConfig.REDIS_PASSWORD,
              };

              // Only add TLS if explicitly enabled
              if (envConfig.REDIS_TLS === true) {
                clusterRedisOptions.tls = {};
              }

              config.cluster = {
                nodes: clusterNodes,
                redisOptions: clusterRedisOptions,
              };
            }
          }
        }

        return config;
      },
    }),

    CacheModule,
    CacheExampleModule,
  ],
  controllers: [AppController, OrganisationController],
  providers: [
    AppService,
    BullBoardAuthMiddleware,
    { provide: APP_GUARD, useClass: AuthGuard },
    { provide: APP_GUARD, useClass: ClientTypeGuard },
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
  ],
  exports: [ThrottlerModule],
})
export class AppModule {}
