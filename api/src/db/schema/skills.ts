import { uuid, text, date, pgTable } from 'drizzle-orm/pg-core';

import { sql } from 'drizzle-orm';
import { createSelectSchema } from 'drizzle-zod';
import { student_profiles } from './student_profile';
import { primaryKey } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

export const skillProficiency = [
  'beginner',
  'intermediate',
  'advanced',
  'expert',
] as const;

export const skillCategory = pgTable('skillCategories', {
  id: uuid('id')
    .primaryKey()
    .default(sql`gen_random_uuid()`),
  skillCategoryName: text('skillCategoryName').notNull(),
  created_at: date('created_at').notNull().defaultNow(),
  updated_at: date('updated_at').defaultNow(),
});

export const skills = pgTable('skills', {
  id: uuid('id')
    .primaryKey()
    .default(sql`gen_random_uuid()`),
  skillName: text('skillName').notNull(),
  skillCategoryId: uuid('skillCategoryId')
    .notNull()
    .references(() => skillCategory.id, {
      onDelete: 'cascade',
    }),
  specialization: text('specialization'),
  created_at: date('created_at').notNull().defaultNow(),
  updated_at: date('updated_at').defaultNow(),
});

export const studentSkills = pgTable(
  'studentSkills',
  {
    studentId: uuid('student_profile_id')
      .notNull()
      .references(() => student_profiles.id, { onDelete: 'cascade' }),
    skillId: uuid('skillId')
      .notNull()
      .references(() => skills.id, {
        onDelete: 'cascade',
      }),
    proficiency: text({ enum: skillProficiency }),

    created_at: date('created_at').notNull().defaultNow(),
    updated_at: date('updated_at').defaultNow(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.studentId, table.skillId] }),
  }),
);

export const skillsRelations = relations(skills, ({ one, many }) => ({
  skillCategory: one(skillCategory, {
    fields: [skills.skillCategoryId],
    references: [skillCategory.id],
  }),
}));

export const skillCategoryRelations = relations(skillCategory, ({ many }) => ({
  skills: many(skills),
}));

export const studentSkillsRelations = relations(studentSkills, ({ one }) => ({
  studentProfile: one(student_profiles, {
    fields: [studentSkills.studentId],
    references: [student_profiles.id],
  }),
  skill: one(skills, {
    fields: [studentSkills.skillId],
    references: [skills.id],
  }),
}));

export const skillKeys = Object.keys(skills) as [string, ...string[]];

export const selectSkillsSchema = createSelectSchema(skills);
export const selectSkillCategorySchema = createSelectSchema(skillCategory);
