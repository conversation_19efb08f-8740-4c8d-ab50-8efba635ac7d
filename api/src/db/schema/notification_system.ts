import { relations, sql } from 'drizzle-orm';
import {
  boolean,
  jsonb,
  pgTable,
  text,
  timestamp,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';
import { users } from './users';

// Notification channels
export const notification_channels = [
  'email',
  'push',
  'in_app',
  'slack',
] as const;
export type NotificationChannel = (typeof notification_channels)[number];

// Notification statuses
export const notification_statuses = [
  'pending',
  'sent',
  'failed',
  'cancelled',
] as const;
export type NotificationStatus = (typeof notification_statuses)[number];

// Notification templates table
export const notification_templates = pgTable('notification_templates', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 100 }).notNull().unique(),
  description: text('description').notNull(),
  title_template: text('title_template').notNull(),
  body_template: text('body_template').notNull(),
  email_subject_template: text('email_subject_template'),
  email_body_template: text('email_body_template'),
  created_by: uuid('created_by').references(() => users.id),
  created_at: timestamp('created_at', { mode: 'string' })
    .notNull()
    .defaultNow(),
  updated_at: timestamp('updated_at', { mode: 'string' })
    .notNull()
    .default(sql`now()`)
    .$onUpdate(() => sql`now()`),
});

// Notification types table
export const notification_types = pgTable('notification_types', {
  id: uuid('id').primaryKey().defaultRandom(),
  code: varchar('code', { length: 50 }).notNull().unique(),
  name: varchar('name', { length: 100 }).notNull(),
  description: text('description').notNull(),
  module: varchar('module', { length: 50 }).notNull(),
  template_id: uuid('template_id').references(() => notification_templates.id),
  default_channels: text('default_channels').array().notNull().default([]),
  created_at: timestamp('created_at', { mode: 'string' })
    .notNull()
    .defaultNow(),
  updated_at: timestamp('updated_at', { mode: 'string' })
    .notNull()
    .default(sql`now()`)
    .$onUpdate(() => sql`now()`),
});

// User notification preferences table
export const notification_preferences = pgTable('notification_preferences', {
  id: uuid('id').primaryKey().defaultRandom(),
  user_id: uuid('user_id')
    .references(() => users.id, { onDelete: 'cascade' })
    .notNull(),
  notification_type_id: uuid('notification_type_id')
    .references(() => notification_types.id, { onDelete: 'cascade' })
    .notNull(),
  email_enabled: boolean('email_enabled').notNull().default(true),
  push_enabled: boolean('push_enabled').notNull().default(true),
  in_app_enabled: boolean('in_app_enabled').notNull().default(true),
  created_at: timestamp('created_at', { mode: 'string' })
    .notNull()
    .defaultNow(),
  updated_at: timestamp('updated_at', { mode: 'string' })
    .notNull()
    .default(sql`now()`)
    .$onUpdate(() => sql`now()`),
});

// Notification logs table
export const notification_logs = pgTable('notification_logs', {
  id: uuid('id').primaryKey().defaultRandom(),
  notification_type_id: uuid('notification_type_id').references(
    () => notification_types.id,
  ),
  user_id: uuid('user_id').references(() => users.id, { onDelete: 'set null' }),
  title: text('title').notNull(),
  body: text('body').notNull(),
  data: jsonb('data'),
  channels: text('channels').array().notNull(),
  status: varchar('status', { length: 20 }).notNull().default('sent'),
  error: text('error'),
  created_at: timestamp('created_at', { mode: 'string' })
    .notNull()
    .defaultNow(),
  read_at: timestamp('read_at', { mode: 'string' }),
});

// Scheduled notifications table
export const scheduled_notifications = pgTable('scheduled_notifications', {
  id: uuid('id').primaryKey().defaultRandom(),
  notification_type_id: uuid('notification_type_id').references(
    () => notification_types.id,
  ),
  title: text('title').notNull(),
  body: text('body').notNull(),
  data: jsonb('data'),
  channels: text('channels').array().notNull(),
  target_audience: jsonb('target_audience').notNull(), // {roles: ['student'], filters: {}}
  scheduled_for: timestamp('scheduled_for', { mode: 'string' }).notNull(),
  status: varchar('status', { length: 20 }).notNull().default('pending'),
  created_by: uuid('created_by')
    .references(() => users.id)
    .notNull(),
  created_at: timestamp('created_at', { mode: 'string' })
    .notNull()
    .defaultNow(),
  updated_at: timestamp('updated_at', { mode: 'string' })
    .notNull()
    .default(sql`now()`)
    .$onUpdate(() => sql`now()`),
});

// Device tokens table for push notifications
export const device_tokens = pgTable('device_tokens', {
  id: uuid('id').primaryKey().defaultRandom(),
  user_id: uuid('user_id')
    .references(() => users.id, { onDelete: 'cascade' })
    .notNull(),
  token: text('token').notNull(),
  device_type: varchar('device_type', { length: 20 }).notNull(), // 'ios', 'android', 'web'
  created_at: timestamp('created_at', { mode: 'string' })
    .notNull()
    .defaultNow(),
  updated_at: timestamp('updated_at', { mode: 'string' })
    .notNull()
    .default(sql`now()`)
    .$onUpdate(() => sql`now()`),
});

// Relations
export const notificationTemplatesRelations = relations(
  notification_templates,
  ({ one, many }) => ({
    createdBy: one(users, {
      fields: [notification_templates.created_by],
      references: [users.id],
    }),
    notificationTypes: many(notification_types),
  }),
);

export const notificationTypesRelations = relations(
  notification_types,
  ({ one, many }) => ({
    template: one(notification_templates, {
      fields: [notification_types.template_id],
      references: [notification_templates.id],
    }),
    preferences: many(notification_preferences),
    logs: many(notification_logs),
    scheduledNotifications: many(scheduled_notifications),
  }),
);

export const notificationPreferencesRelations = relations(
  notification_preferences,
  ({ one }) => ({
    user: one(users, {
      fields: [notification_preferences.user_id],
      references: [users.id],
    }),
    notificationType: one(notification_types, {
      fields: [notification_preferences.notification_type_id],
      references: [notification_types.id],
    }),
  }),
);

export const notificationLogsRelations = relations(
  notification_logs,
  ({ one }) => ({
    user: one(users, {
      fields: [notification_logs.user_id],
      references: [users.id],
    }),
    notificationType: one(notification_types, {
      fields: [notification_logs.notification_type_id],
      references: [notification_types.id],
    }),
  }),
);

export const scheduledNotificationsRelations = relations(
  scheduled_notifications,
  ({ one }) => ({
    createdBy: one(users, {
      fields: [scheduled_notifications.created_by],
      references: [users.id],
    }),
    notificationType: one(notification_types, {
      fields: [scheduled_notifications.notification_type_id],
      references: [notification_types.id],
    }),
  }),
);

export const deviceTokensRelations = relations(device_tokens, ({ one }) => ({
  user: one(users, {
    fields: [device_tokens.user_id],
    references: [users.id],
  }),
}));
