import {
  primaryKey,
  text,
  varchar,
  pgTable,
  time,
  uuid,
  boolean,
  timestamp,
  integer,
} from 'drizzle-orm/pg-core';
import { users } from './users';
import { relations, sql } from 'drizzle-orm';
import { createInsertSchema } from 'drizzle-zod';
import { countries } from './countries';
import { institutions } from './institution';
import { student_clubs } from './clubs';
import { postEngagementsSchema } from './post_engagements';

export const postType = ['event', 'opportunity', 'general'] as const;
export const postStatus = [
  'draft',
  'active',
  'expired',
  'pending',
  'scheduled',
] as const;

export const posts = pgTable('post', {
  id: uuid('id')
    .primaryKey()
    .default(sql`gen_random_uuid()`),
  title: varchar('title').notNull(),
  description: text('description').notNull(),
  imageUrl: text('imageUrl'),
  status: text({ enum: postStatus }).default('active'),
  disabled: boolean('disabled').default(false),
  type: text({ enum: postType }).default('general'),
  postedBy: uuid('postedBy')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  isGlobal: boolean('isGlobal').default(false),
  notify_users: boolean('notify_users').default(false),
  club_id: uuid('club_id').references(() => student_clubs.id, {
    onDelete: 'cascade',
  }),
  scheduled_at: timestamp('scheduledAt', { mode: 'string' }),
  created_at: timestamp('created_at', { mode: 'string' })
    .notNull()
    .defaultNow(),
  updated_at: timestamp('updated_at', { mode: 'string' })
    .notNull()
    .default(sql`now()`)
    .$onUpdate(() => sql`now()`),
});

export const postCountries = pgTable(
  'post_to_countries',
  {
    postId: uuid('postId')
      .notNull()
      .references(() => posts.id, { onDelete: 'cascade' }),
    countryId: uuid('countryId')
      .notNull()
      .references(() => countries.id, { onDelete: 'cascade' }),
  },
  (table) => ({
    pk: primaryKey({
      columns: [table.postId, table.countryId],
    }),
  }),
);

export const postInstitutions = pgTable(
  'post_to_institutions',
  {
    postId: uuid('postId')
      .notNull()
      .references(() => posts.id, { onDelete: 'cascade' }),
    institutionId: uuid('institutionId')
      .notNull()
      .references(() => institutions.id, { onDelete: 'cascade' }),
  },
  (table) => ({
    pk: primaryKey({
      columns: [table.postId, table.institutionId],
    }),
  }),
);

export const opportunity = pgTable('opportunity', {
  id: uuid('id')
    .primaryKey()
    .default(sql`gen_random_uuid()`),
  eligibility: integer('eligibility')
    .array()
    .notNull()
    .default(sql`ARRAY[]::integer[]`),
  applicationUrl: text('applicationUrl'),
  startDate: timestamp('startingDate', {
    mode: 'string',
  }),
  startTime: time('startingTime', {
    withTimezone: false,
  }),
  endDate: timestamp('endingDate', {
    mode: 'string',
  }),
  endTime: time('endingTime', {
    withTimezone: false,
  }),
  postId: uuid('postId')
    .notNull()
    .references(() => posts.id, { onDelete: 'cascade' }),
  created_at: timestamp('created_at', { mode: 'string' })
    .notNull()
    .defaultNow(),
  updated_at: timestamp('updated_at', { mode: 'string' })
    .notNull()
    .default(sql`now()`)
    .$onUpdate(() => sql`now()`),
});

export const events = pgTable('event', {
  id: uuid('id')
    .primaryKey()
    .default(sql`gen_random_uuid()`),
  startDate: timestamp('startingDate', {
    mode: 'string',
  }),
  startTime: time('startingTime', {
    withTimezone: false,
  }),
  endDate: timestamp('endingDate', {
    mode: 'string',
  }),
  endTime: time('endingTime', {
    withTimezone: false,
  }),
  virtualLink: text('virtualLink'),
  postId: uuid('postId')
    .notNull()
    .references(() => posts.id, { onDelete: 'cascade' }),
  created_at: timestamp('created_at', { mode: 'string' })
    .notNull()
    .defaultNow(),
  updated_at: timestamp('updated_at', { mode: 'string' })
    .notNull()
    .default(sql`now()`)
    .$onUpdate(() => sql`now()`),
});

export const postImages = pgTable('post_images', {
  id: uuid('id')
    .primaryKey()
    .default(sql`gen_random_uuid()`),
  postId: uuid('postId')
    .notNull()
    .references(() => posts.id, { onDelete: 'cascade' }),
  imageUrl: text('imageUrl').notNull(),
  created_at: timestamp('created_at', { mode: 'string' })
    .notNull()
    .defaultNow(),
  updated_at: timestamp('updated_at', { mode: 'string' })
    .notNull()
    .default(sql`now()`)
    .$onUpdate(() => sql`now()`),
});

export const eventRelation = relations(events, ({ one }) => ({
  post: one(posts, {
    fields: [events.postId],
    references: [posts.id],
  }),
}));

export const opportunityRelations = relations(opportunity, ({ one }) => ({
  post: one(posts, {
    fields: [opportunity.postId],
    references: [posts.id],
  }),
}));

export const postRelations = relations(posts, ({ one, many }) => ({
  postedBy: one(users, {
    fields: [posts.postedBy],
    references: [users.id],
  }),
  countries: many(postCountries),
  institutions: many(postInstitutions),
  opportunity: one(opportunity),
  event: one(events),
  club: one(student_clubs, {
    fields: [posts.club_id],
    references: [student_clubs.id],
  }),
  postEngagements: many(postEngagementsSchema),
  images: many(postImages),
}));

export const postToCountriesRelations = relations(postCountries, ({ one }) => ({
  post: one(posts, {
    fields: [postCountries.postId],
    references: [posts.id],
  }),
  country: one(countries, {
    fields: [postCountries.countryId],
    references: [countries.id],
  }),
}));

export const postToInstitutionRelations = relations(
  postInstitutions,
  ({ one }) => ({
    post: one(posts, {
      fields: [postInstitutions.postId],
      references: [posts.id],
    }),
    institution: one(institutions, {
      fields: [postInstitutions.institutionId],
      references: [institutions.id],
    }),
  }),
);

export const postImagesRelations = relations(postImages, ({ one }) => ({
  post: one(posts, {
    fields: [postImages.postId],
    references: [posts.id],
  }),
}));

export const insertPostSchema = createInsertSchema(posts).omit({
  postedBy: true,
});
export const insertOpportunitySchema = createInsertSchema(opportunity);
export const insertEventSchema = createInsertSchema(events);

export type Post = typeof posts.$inferSelect;
export type IEvent = typeof events.$inferSelect;
export type insertPostInput = typeof posts.$inferInsert;
export type insertOpportunityInput = typeof opportunity.$inferInsert;
export type insertEventInput = typeof events.$inferInsert;

export const post_types = {
  EVENT: 'event',
  OPPORTUNITY: 'opportunity',
  GENERAL: 'general',
} as const;

export const post_statuses = {
  DRAFT: 'draft',
  ACTIVE: 'active',
  EXPIRED: 'expired',
  PENDING: 'pending',
  SCHEDULED: 'scheduled',
} as const;

export type PostType = (typeof post_types)[keyof typeof post_types];
export type PostStatus = (typeof post_statuses)[keyof typeof post_statuses];
