import { skills, skillCategory, studentSkills } from '@/db/schema/skills';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import {
  Injectable,
  Logger,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import {
  eq,
  and,
  ne,
  getTableColumns,
  asc,
  ilike,
  or,
  desc,
} from 'drizzle-orm';
import {
  SkillDto,
  SkillParam,
  SkillCategoryDto,
  SkillCategoryParam,
  SkillQueryParamsDto,
} from './skills.dto';
import { CacheService } from '@app/shared/cache/cache.service';
import { Cacheable } from '@app/shared/cache/decorators/cacheable.decorator';
import { CacheInvalidate } from '@app/shared/cache/decorators/cache-invalidate.decorator';
import {
  CACHE_PREFIXES,
  CACHE_TTL,
} from '@app/shared/constants/cache.constant';
import { SkillServiceMessages } from '@app/shared/constants/skills.constants';
import { user_roles, type User as UserDecoratorType } from '@/db/schema';

@Injectable()
export class SkillsService {
  private readonly logger = new Logger(SkillsService.name);

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly cacheService: CacheService,
  ) {}

  /**
   * Check if skill name already exists
   */
  private async checkSkillNameExists(
    skillName: string,
    excludeId?: string,
  ): Promise<boolean> {
    const conditions = [eq(skills.skillName, skillName)];
    if (excludeId) {
      conditions.push(ne(skills.id, excludeId));
    }

    const existingSkill = await this.drizzle.db
      .select({ id: skills.id })
      .from(skills)
      .where(and(...conditions))
      .limit(1);

    return existingSkill.length > 0;
  }

  /**
   * Check if skill category name already exists
   */
  private async checkSkillCategoryNameExists(
    categoryName: string,
    excludeId?: string,
  ): Promise<boolean> {
    const conditions = [eq(skillCategory.skillCategoryName, categoryName)];
    if (excludeId) {
      conditions.push(ne(skillCategory.id, excludeId));
    }

    const existingCategory = await this.drizzle.db
      .select({ id: skillCategory.id })
      .from(skillCategory)
      .where(and(...conditions))
      .limit(1);

    return existingCategory.length > 0;
  }
  /**
   * Add a new skill (invalidates cache)
   */
  @CacheInvalidate({
    prefix: CACHE_PREFIXES.SKILL,
    keys: ['all'],
    afterExecution: true,
  })
  async addSkill(skillInput: SkillDto): Promise<SkillParam> {
    const { skillName, skillCategoryId, specialization } = skillInput;

    // Check if skill name already exists
    const nameExists = await this.checkSkillNameExists(skillName);
    if (nameExists) {
      throw new ConflictException(
        SkillServiceMessages.SkillNameExists(skillName),
      );
    }

    const skillCategoryExists = await this.drizzle.db
      .select({ id: skillCategory.id })
      .from(skillCategory)
      .where(eq(skillCategory.id, skillCategoryId))
      .limit(1);

    if (skillCategoryExists.length === 0)
      throw new BadRequestException(
        SkillServiceMessages.SkillCategoryNotFound(skillCategoryId),
      );

    const [newSkill] = await this.drizzle.db
      .insert(skills)
      .values({
        skillName,
        skillCategoryId,
        specialization,
      })
      .returning();

    if (!newSkill) {
      throw new BadRequestException(SkillServiceMessages.FailedToCreateSkill);
    }

    this.logger.log('Created new skill successfully.');
    return newSkill;
  }
  /**
   * Get skill by ID (cached for 30 minutes)
   */
  @Cacheable({
    prefix: CACHE_PREFIXES.SKILL,
    ttl: CACHE_TTL.THIRTY_MINUTES,
    keyGenerator: (args) => [args[0]],
    condition: (args) => !!args[0],
  })
  async getSkillById(skillId: string): Promise<SkillParam> {
    const [skill] = await this.drizzle.db
      .select({
        ...getTableColumns(skills),
        skillCategory: {
          ...getTableColumns(skillCategory),
        },
      })
      .from(skills)
      .where(eq(skills.id, skillId))
      .leftJoin(skillCategory, eq(skills.skillCategoryId, skillCategory.id));
    if (!skill) {
      throw new BadRequestException(SkillServiceMessages.SkillNotFound);
    }
    this.logger.log('Fetched skill successfully.');
    return skill;
  }
  /**
   * Update a skill (invalidates specific caches)
   */
  @CacheInvalidate({
    prefix: CACHE_PREFIXES.SKILL,
    keys: (args) => ['all', args[0]],
    afterExecution: true,
  })
  async updateSkill(
    skillId: string,
    skillInput: SkillDto,
  ): Promise<SkillParam> {
    const { skillName, skillCategoryId, specialization } = skillInput;

    // Check if skill name already exists (excluding current skill)
    const nameExists = await this.checkSkillNameExists(skillName, skillId);
    if (nameExists) {
      throw new ConflictException(
        SkillServiceMessages.SkillNameExists(skillName),
      );
    }

    const skillCategoryExists = await this.drizzle.db
      .select({ id: skillCategory.id })
      .from(skillCategory)
      .where(eq(skillCategory.id, skillCategoryId))
      .limit(1);
    if (skillCategoryExists.length === 0)
      throw new BadRequestException(
        SkillServiceMessages.SkillCategoryNotFound(skillCategoryId),
      );

    const [updatedSkill] = await this.drizzle.db
      .update(skills)
      .set({
        skillName,
        skillCategoryId,
        specialization,
      })
      .where(eq(skills.id, skillId))
      .returning();

    if (!updatedSkill) {
      throw new BadRequestException(SkillServiceMessages.FailedToUpdateSkill);
    }
    this.logger.log('Updated skill successfully.');
    return updatedSkill;
  }

  /**
   * Delete a skill (invalidates specific caches)
   */
  @CacheInvalidate({
    prefix: CACHE_PREFIXES.SKILL,
    keys: (args) => ['all', args[0]],
    afterExecution: true,
  })
  async deleteSkill(skillId: string, user: UserDecoratorType) {
    if (
      user.role === user_roles.STUDENT ||
      user.role === user_roles.STUDENT_ADMIN
    ) {
      const [userHasSkill] = await this.drizzle.db
        .select({
          skillId: studentSkills.skillId,
        })
        .from(studentSkills)
        .where(
          and(
            eq(studentSkills.studentId, user.student_profile!.id),
            eq(studentSkills.skillId, skillId),
          ),
        );

      if (!userHasSkill) {
        throw new BadRequestException(
          `You do not have the skill with ID '${skillId}' to delete.`,
        );
      }

      // If the user is a student, we only allow them to delete their own skills
      const [deletedSkill] = await this.drizzle.db
        .delete(studentSkills)
        .where(
          and(
            eq(studentSkills.studentId, user.student_profile!.id),
            eq(studentSkills.skillId, skillId),
          ),
        )
        .returning();
      if (!deletedSkill) {
        throw new BadRequestException(SkillServiceMessages.FailedToDeleteSkill);
      }
      this.logger.log('Deleted student skill successfully.');
      return deletedSkill;
    }

    const [deletedSkill] = await this.drizzle.db
      .delete(skills)
      .where(eq(skills.id, skillId))
      .returning();
    if (!deletedSkill) {
      throw new BadRequestException(SkillServiceMessages.FailedToDeleteSkill);
    }
    this.logger.log('Deleted skill successfully.');
    return deletedSkill;
  }
  /**
   * Get all skills (cached for 30 minutes)
   */
  // @Cacheable({
  //   prefix: CACHE_PREFIXES.SKILL,
  //   ttl: CACHE_TTL.THIRTY_MINUTES,
  //   keyGenerator: () => ['all'],
  // })
  async getAllSkills({
    page,
    limit,
    sort,
    order,
    search,
  }: SkillQueryParamsDto & {
    sort: keyof SkillParam;
  }): Promise<SkillParam[]> {
    const filters = [];

    if (search) {
      const searchPattern = `%${search.toString().toLocaleLowerCase().trim()}%`;

      filters.push(
        or(
          ilike(skills.skillName, searchPattern),
          ilike(skillCategory.skillCategoryName, searchPattern),
          ilike(skills.specialization, searchPattern),
        ),
      );
    }

    const allSkills: SkillParam[] = await this.drizzle.db
      .select({
        ...getTableColumns(skills),
        skillCategory: {
          ...getTableColumns(skillCategory),
        },
      })
      .from(skills)
      .where(and(...filters))
      .leftJoin(skillCategory, eq(skills.skillCategoryId, skillCategory.id))
      .orderBy(
        order === 'asc' ? asc(skills[sort]) : desc(skills[sort]),
        skills.id,
      )
      .limit(limit)
      .offset((page - 1) * limit);
    this.logger.log('Fetched all skills successfully.');
    return allSkills;
  }
  /**
   * Get skills by skill category (cached for 30 minutes)
   */
  @Cacheable({
    prefix: CACHE_PREFIXES.SKILL,
    ttl: CACHE_TTL.THIRTY_MINUTES,
    keyGenerator: (args) => ['category', args[0]],
    condition: (args) => !!args[0],
  })
  async getSkillsByCategory(categoryId: string): Promise<SkillParam[]> {
    const skillsByCategory = await this.drizzle.db.query.skills.findMany({
      where: eq(skills.skillCategoryId, categoryId),
      with: {
        skillCategory: true,
      },
    });

    this.logger.log(`Fetched skills for category ${categoryId} successfully.`);
    return skillsByCategory;
  }

  @Cacheable({
    prefix: CACHE_PREFIXES.SKILL,
    ttl: CACHE_TTL.THIRTY_MINUTES,
    keyGenerator: (args) => ['student', args[0]],
    condition: (args) => !!args[0],
  })
  async getStudentSkills(studentId: string): Promise<SkillParam[]> {
    const studentSkillsData = await this.drizzle.db
      .select({
        ...getTableColumns(skills),
        proficiency: studentSkills.proficiency,
        skillCategory: {
          ...getTableColumns(skillCategory),
        },
      })
      .from(studentSkills)
      .innerJoin(skills, eq(studentSkills.skillId, skills.id))
      .leftJoin(skillCategory, eq(skills.skillCategoryId, skillCategory.id))
      .where(eq(studentSkills.studentId, studentId))
      .orderBy(asc(skills.skillName));

    this.logger.log(`Fetched skills for student ${studentId} successfully.`);
    return studentSkillsData;
  }

  // Skill Category methods
  /**
   * Add a new skill category (invalidates cache)
   */ @CacheInvalidate({
    prefix: CACHE_PREFIXES.SKILL_CATEGORY,
    keys: ['all'],
    afterExecution: true,
  })
  async addSkillCategory(
    skillCategoryInput: SkillCategoryDto,
  ): Promise<SkillCategoryParam> {
    const { skillCategoryName } = skillCategoryInput;

    // Check if skill category name already exists
    const nameExists =
      await this.checkSkillCategoryNameExists(skillCategoryName);
    if (nameExists) {
      throw new ConflictException(
        SkillServiceMessages.SkillCategoryNameExists(skillCategoryName),
      );
    }
    const [newSkillCategory] = await this.drizzle.db
      .insert(skillCategory)
      .values({
        skillCategoryName,
      })
      .returning();

    if (!newSkillCategory) {
      throw new BadRequestException(
        SkillServiceMessages.FailedToCreateSkillCategory,
      );
    }

    this.logger.log('Created new skill category successfully.');
    return newSkillCategory;
  }
  /**
   * Get skill category by ID (cached for 30 minutes)
   */
  @Cacheable({
    prefix: CACHE_PREFIXES.SKILL_CATEGORY,
    ttl: CACHE_TTL.THIRTY_MINUTES,
    keyGenerator: (args) => [args[0]],
    condition: (args) => !!args[0],
  })
  async getSkillCategoryById(categoryId: string): Promise<SkillCategoryParam> {
    const [category] = await this.drizzle.db
      .select()
      .from(skillCategory)
      .where(eq(skillCategory.id, categoryId));
    if (!category) {
      throw new BadRequestException(
        SkillServiceMessages.SkillCategoryNotFound(categoryId),
      );
    }
    this.logger.log('Fetched skill category successfully.');
    return category;
  }
  /**
   * Get all skill categories (cached for 30 minutes)
   */
  @Cacheable({
    prefix: CACHE_PREFIXES.SKILL_CATEGORY,
    ttl: CACHE_TTL.THIRTY_MINUTES,
    keyGenerator: () => ['all'],
  })
  async getAllSkillCategories(): Promise<SkillCategoryParam[]> {
    const categories: SkillCategoryParam[] = await this.drizzle.db
      .select()
      .from(skillCategory);
    this.logger.log('Fetched all skill categories successfully.');
    return categories;
  }
  /**
   * Update a skill category (invalidates specific caches)
   */
  @CacheInvalidate({
    prefix: CACHE_PREFIXES.SKILL_CATEGORY,
    keys: (args) => ['all', args[0]],
    afterExecution: true,
  })
  async updateSkillCategory(
    categoryId: string,
    skillCategoryInput: SkillCategoryDto,
  ): Promise<SkillCategoryParam> {
    const { skillCategoryName } = skillCategoryInput;

    // Check if skill category name already exists (excluding current category)
    const nameExists = await this.checkSkillCategoryNameExists(
      skillCategoryName,
      categoryId,
    );
    if (nameExists) {
      throw new ConflictException(
        SkillServiceMessages.SkillCategoryNameExists(skillCategoryName),
      );
    }

    const [updatedCategory] = await this.drizzle.db
      .update(skillCategory)
      .set({
        skillCategoryName,
      })
      .where(eq(skillCategory.id, categoryId))
      .returning();

    if (!updatedCategory) {
      throw new BadRequestException(
        SkillServiceMessages.FailedToUpdateSkillCategory,
      );
    }
    this.logger.log('Updated skill category successfully.');
    return updatedCategory;
  }

  /**
   * Delete a skill category (invalidates specific caches)
   */
  @CacheInvalidate({
    prefix: CACHE_PREFIXES.SKILL_CATEGORY,
    keys: (args) => ['all', args[0]],
    afterExecution: true,
  })
  async deleteSkillCategory(categoryId: string): Promise<SkillCategoryParam[]> {
    const deletedCategory = await this.drizzle.db
      .delete(skillCategory)
      .where(eq(skillCategory.id, categoryId))
      .returning();
    if (!deletedCategory || deletedCategory.length === 0) {
      // Check if array is empty
      throw new BadRequestException(
        SkillServiceMessages.FailedToDeleteSkillCategory,
      );
    }
    this.logger.log('Deleted skill category successfully.');
    return deletedCategory;
  }

  @CacheInvalidate({
    prefix: CACHE_PREFIXES.SKILL,
    keys: (args) => ['student', args[0]],
    afterExecution: true,
  })
  async addStudentSkill({
    studentId,
    skills,
  }: {
    studentId: string;
    skills: Array<{
      skillId: string;
      proficiency?: 'beginner' | 'intermediate' | 'advanced' | 'expert';
    }>;
  }): Promise<void> {
    // Get existing skills for the student
    const existingSkills = await this.drizzle.db
      .select({ skillId: studentSkills.skillId })
      .from(studentSkills)
      .where(eq(studentSkills.studentId, studentId));

    const existingSkillIds = existingSkills.map((skill) => skill.skillId);

    // Filter out skills that already exist
    const newSkills = skills.filter(
      (skill) => !existingSkillIds.includes(skill.skillId),
    );

    if (newSkills.length === 0) {
      throw new BadRequestException(
        'All skills already exist for this student',
      );
    }

    // Add new skills
    await this.drizzle.db.insert(studentSkills).values(
      newSkills.map((skill) => ({
        studentId,
        skillId: skill.skillId,
        proficiency: skill.proficiency,
      })),
    );

    const skippedCount = skills.length - newSkills.length;

    this.logger.log(
      `Added ${newSkills.length} skills for student with ID '${studentId}', skipped ${skippedCount} existing skills`,
    );
  }

  @CacheInvalidate({
    prefix: CACHE_PREFIXES.SKILL,
    keys: (args) => ['student', args[0]],
    afterExecution: true,
  })
  async removeStudentSkill(studentId: string, skillId: string): Promise<void> {
    // Check if the student has this skill
    const existingSkill = await this.drizzle.db
      .select({ skillId: studentSkills.skillId })
      .from(studentSkills)
      .where(
        and(
          eq(studentSkills.studentId, studentId),
          eq(studentSkills.skillId, skillId),
        ),
      )
      .limit(1);

    if (existingSkill.length === 0) {
      throw new BadRequestException(
        `Student with ID '${studentId}' does not have skill with ID '${skillId}'`,
      );
    }

    // Remove the skill for the student
    await this.drizzle.db
      .delete(studentSkills)
      .where(
        and(
          eq(studentSkills.studentId, studentId),
          eq(studentSkills.skillId, skillId),
        ),
      );

    this.logger.log(
      `Removed skill with ID '${skillId}' for student with ID '${studentId}'`,
    );
  }
}
