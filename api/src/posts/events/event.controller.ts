import {
  Controller,
  Post,
  Put,
  Get,
  Body,
  Logger,
  UseInterceptors,
  UploadedFiles,
  UseGuards,
  Param,
  ParseFilePipeBuilder,
} from '@nestjs/common';
import { ZodSerializerDto } from 'nestjs-zod';
import {
  ApiCreatedResponse,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiBody,
  ApiBearerAuth,
  ApiConsumes,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { eventDto, postDto } from '../dto/post.dto';
import { User } from '@/guards/user.decorator';
import { type User as UserDecoratorType } from '@/db/schema';
import { UseRoles } from 'nest-access-control';
import { FilesInterceptor, FileInterceptor } from '@nestjs/platform-express';
import { CustomMaxFileSizeValidator } from '@/validators/custom-max-file-size.validator';
import { RoleGuard } from '@/guards/role.guard';
import { CustomFileUploadValidator } from '@/validators/custom-file-type.validator';
import {
  PostRoutes,
  PostServiceMessages,
} from '@app/shared/constants/post.constants';
import { PostService } from '../post.service';
import { clubEventDto } from '../dto/event.dto';
import { EventService } from './event.service';
import { CustomParseUUIDPipe } from '@/common/pipes/custom-parse-uuid';

@ApiTags('Events')
@ApiBearerAuth()
@Controller({ version: '1', path: 'event' })
export class EventsController {
  private readonly logger = new Logger(PostService.name);
  constructor(private eventService: EventService) {}

  @Post()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'post', action: 'create', possession: 'any' })
  @ZodSerializerDto(postDto)
  @ApiOperation({
    summary: 'Create a new event',
    description:
      'Create a new event with optional attachments and notification settings',
  })
  @ApiBody({
    type: eventDto,
    description: 'Event data to create',
  })
  @ApiCreatedResponse({
    description: 'The record has been successfully created.',
    type: postDto,
  })
  @UseInterceptors(FilesInterceptor('attachments'))
  async createEvent(
    @User() user: UserDecoratorType,
    @Body() data: eventDto,
    @UploadedFiles(
      new ParseFilePipeBuilder()
        .addValidator(
          new CustomMaxFileSizeValidator({
            maxSize: 2 * 1024 * 1024,
          }),
        )
        .addValidator(new CustomFileUploadValidator({ isRequired: false }))
        .build({ fileIsRequired: false }),
    )
    attachments: Express.Multer.File[] = [],
  ) {
    try {
      return this.eventService.createEvent(data, user, attachments);
    } catch (error: any) {
      this.logger.error('Error creating event: ', error.stack);
      throw error;
    }
  }

  @Post(PostRoutes.CLUB_EVENT)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'post', action: 'create', possession: 'any' })
  @ZodSerializerDto(clubEventDto)
  @ApiOperation({
    summary: 'Create club event',
    description: 'Create a new event associated with a specific club',
  })
  @ApiParam({
    name: 'clubId',
    description: 'Club unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: clubEventDto,
    description: 'Club event data to create',
  })
  @ApiCreatedResponse({
    description: 'The record has been successfully created.',
    type: clubEventDto,
  })
  @ApiNotFoundResponse({
    description: PostServiceMessages.ClubNotFound,
  })
  @ApiUnauthorizedResponse({
    description: PostServiceMessages.UnAuthorized,
  })
  @UseInterceptors(FilesInterceptor('attachments'))
  async createClubEvent(
    @User() user: UserDecoratorType,
    @Body() data: clubEventDto,
    @Param('clubId', new CustomParseUUIDPipe()) clubId: string,
    @UploadedFiles(
      new ParseFilePipeBuilder()
        .addValidator(
          new CustomMaxFileSizeValidator({
            maxSize: 2 * 1024 * 1024,
          }),
        )
        .addValidator(new CustomFileUploadValidator({ isRequired: false }))
        .build({ fileIsRequired: false }),
    )
    attachments: Express.Multer.File[] = [],
  ) {
    try {
      return this.eventService.createClubEvent(data, user, clubId, attachments);
    } catch (error: any) {
      this.logger.error("Error creating club's event: ", error.stack);
      throw error;
    }
  }

  @Put(PostRoutes.UPDATE_EVENT)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'post', action: 'update', possession: 'any' })
  @ZodSerializerDto(eventDto)
  @ApiOperation({
    summary: 'Update event',
    description: 'Update an existing event by ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Event unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: eventDto,
    description: 'Updated event data',
  })
  @ApiCreatedResponse({
    description: 'The record has been successfully updated.',
    type: eventDto,
  })
  @ApiNotFoundResponse({
    description: PostServiceMessages.PostNotFound,
  })
  @ApiUnauthorizedResponse({
    description: PostServiceMessages.UnAuthorized,
  })
  @ApiInternalServerErrorResponse({
    description: 'Failed to update event',
  })
  @UseInterceptors(FileInterceptor, FileInterceptor('attachments'))
  async updateEvent(
    @User() user: UserDecoratorType,
    @Body() data: eventDto,
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @UploadedFiles(
      new ParseFilePipeBuilder()
        .addValidator(
          new CustomMaxFileSizeValidator({
            maxSize: 2 * 1024 * 1024,
          }),
        )
        .addValidator(new CustomFileUploadValidator({ isRequired: false }))
        .build({ fileIsRequired: false }),
    )
    attachments: Express.Multer.File[],
  ) {
    try {
      return this.eventService.updateEvent(
        {
          data,
          user,
          attachments,
        },
        id,
      );
    } catch (error: any) {
      this.logger.error('Error updating event: ', error.stack);
      throw error;
    }
  }

  @Get(PostRoutes.GET_EVENT_BY_ID)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'post', action: 'read', possession: 'any' })
  @ApiOperation({
    summary: 'Get event by ID',
    description: 'Retrieve a specific event by its unique identifier',
  })
  @ApiParam({
    name: 'id',
    description: 'Event unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiOkResponse({
    description: 'Successfully retrieved the event.',
    type: eventDto,
  })
  @ApiNotFoundResponse({
    description: 'Event with ID not found',
  })
  async getEventById(@Param('id', new CustomParseUUIDPipe()) id: string) {
    try {
      return await this.eventService.getEventById(id);
    } catch (error: any) {
      this.logger.error(`Error retrieving event with ID ${id}`, error.stack);
      throw error;
    }
  }
}
