import { Test, TestingModule } from '@nestjs/testing';
import { EventService } from './event.service';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { PostRepository } from '@/repositories/post.repository';
import { PostService } from '../post.service';
import { CacheService } from '@app/shared/redis/cache.service';
import { NotFoundException } from '@nestjs/common';

describe('EventService', () => {
  let service: EventService;

  const mockDrizzleService = {
    db: {
      query: {
        events: {
          findFirst: jest.fn(),
        },
      },
      select: jest.fn().mockReturnThis(),
      from: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      then: jest.fn().mockResolvedValue([]),
      transaction: jest.fn(),
      insert: jest.fn().mockReturnThis(),
      values: jest.fn().mockReturnThis(),
      returning: jest.fn().mockResolvedValue([]),
      update: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
    },
  };

  const mockCacheService = {
    get: jest.fn().mockResolvedValue(null), // Always return null to bypass cache
    set: jest.fn().mockResolvedValue(undefined),
    del: jest.fn().mockResolvedValue(undefined),
    generateKey: jest
      .fn()
      .mockImplementation(
        (keys, prefix) =>
          `${prefix}:${Array.isArray(keys) ? keys.join(':') : keys}`,
      ),
  };

  const mockPostRepository = {
    createPost: jest.fn(),
    updatePost: jest.fn(),
    updateEvent: jest.fn(),
  };

  const mockPostService = {
    uploadPostAttachments: jest.fn(),
    sendPostNotifications: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EventService,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
        {
          provide: PostRepository,
          useValue: mockPostRepository,
        },
        {
          provide: PostService,
          useValue: mockPostService,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
      ],
    }).compile();

    service = module.get<EventService>(EventService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getEventById', () => {
    const eventId = '123e4567-e89b-12d3-a456-426614174000';
    const mockEvent = {
      id: eventId,
      startDate: '2024-01-01',
      endDate: '2024-01-02',
      startTime: '10:00:00',
      endTime: '12:00:00',
      virtualLink: 'https://example.com',
      postId: 'post-123',
      created_at: '2024-01-01T00:00:00.000Z',
      updated_at: '2024-01-01T00:00:00.000Z',
      post: {
        id: 'post-123',
        title: 'Test Event',
        description: 'Test event description',
        status: 'active',
        type: 'event',
        postedBy: {
          id: 'user-123',
          profile: {
            id: 'profile-123',
            email: '<EMAIL>',
            name: 'Test User',
          },
          student_profile: {
            id: 'student-123',
            first_name: 'Test',
            last_name: 'User',
          },
        },
        countries: [],
        institutions: [],
        postEngagements: [],
        images: [],
        club: null,
      },
    };

    it('should return an event when found', async () => {
      mockDrizzleService.db.query.events.findFirst.mockResolvedValue(mockEvent);

      const result = await service.getEventById(eventId);

      expect(result).toEqual(mockEvent);
      expect(mockDrizzleService.db.query.events.findFirst).toHaveBeenCalledWith(
        {
          where: expect.anything(),
          with: expect.objectContaining({
            post: expect.any(Object),
          }),
        },
      );
    });

    it('should throw NotFoundException when event not found', async () => {
      mockDrizzleService.db.query.events.findFirst.mockResolvedValue(null);

      await expect(service.getEventById(eventId)).rejects.toThrow(
        new NotFoundException(`Event with ID ${eventId} not found`),
      );
      expect(mockDrizzleService.db.query.events.findFirst).toHaveBeenCalledWith(
        {
          where: expect.anything(),
          with: expect.objectContaining({
            post: expect.any(Object),
          }),
        },
      );
    });

    it('should handle invalid UUID format', async () => {
      const invalidId = 'invalid-uuid';
      mockDrizzleService.db.query.events.findFirst.mockResolvedValue(null);

      await expect(service.getEventById(invalidId)).rejects.toThrow(
        new NotFoundException(`Event with ID ${invalidId} not found`),
      );
      expect(mockDrizzleService.db.query.events.findFirst).toHaveBeenCalledWith(
        {
          where: expect.anything(),
          with: expect.objectContaining({
            post: expect.any(Object),
          }),
        },
      );
    });

    it('should have getEventById method defined', () => {
      expect(service.getEventById).toBeDefined();
      expect(typeof service.getEventById).toBe('function');
    });
  });
});
