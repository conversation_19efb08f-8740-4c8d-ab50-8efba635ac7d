import { Test, TestingModule } from '@nestjs/testing';
import { OpportunityController } from './opportunity.controller';
import { OpportunityService } from './opportunity.service';
import { NotFoundException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RolesBuilder } from 'nest-access-control';
import { type User as UserDecoratorType } from '@/db/schema';
import { user_roles } from '@/db/schema/users';

describe('OpportunityController', () => {
  let controller: OpportunityController;
  let opportunityService: OpportunityService;

  const mockOpportunityService = {
    createOpportunity: jest.fn(),
    updateOpportunity: jest.fn(),
    getOpportunityById: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [OpportunityController],
      providers: [
        {
          provide: OpportunityService,
          useValue: mockOpportunityService,
        },
        {
          provide: Reflector,
          useValue: {
            get: jest.fn(),
            getAllAndOverride: jest.fn(),
          },
        },
        {
          provide: '__roles_builder__',
          useValue: new RolesBuilder(),
        },
      ],
    }).compile();

    controller = module.get<OpportunityController>(OpportunityController);
    opportunityService = module.get<OpportunityService>(OpportunityService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getOpportunityById', () => {
    const opportunityId = '123e4567-e89b-12d3-a456-426614174000';
    const mockUser: UserDecoratorType = {
      id: 'user-456',
      email: '<EMAIL>',
      role: user_roles.STUDENT,
      state: 'active',
      profile_pic_url: null,
      created_at: '2024-01-01T00:00:00.000Z',
      updated_at: '2024-01-01T00:00:00.000Z',
      deleted: false,
      deleted_at: null,
      student_profile: {
        id: 'student-456',
        user_id: 'user-456',
        first_name: 'Student',
        last_name: 'User',
        other_name: null,
        username: 'student_user',
        country_id: 'country-123',
        date_of_birth: '1990-01-01',
        phone_number: '+1234567890',
        institution_id: 'institution-123',
        enrollment_date: 2020,
        graduation_date: 2024,
        degree: 'Bachelors',
        programme: 'ICT',
        github_profile: null,
        linkedin_profile: null,
        about: null,
        club_id: null,
        created_at: '2024-01-01T00:00:00.000Z',
        updated_at: '2024-01-01T00:00:00.000Z',
        deleted: false,
        deleted_at: null,
      },
    };

    const mockOpportunity = {
      id: opportunityId,
      eligibility: [1, 2, 3],
      applicationUrl: 'https://example.com/apply',
      startDate: '2024-01-01',
      endDate: '2024-01-31',
      startTime: '09:00:00',
      endTime: '17:00:00',
      postId: 'post-123',
      created_at: '2024-01-01T00:00:00.000Z',
      updated_at: '2024-01-01T00:00:00.000Z',
      post: {
        id: 'post-123',
        title: 'Test Opportunity',
        description: 'Test opportunity description',
        status: 'active',
        type: 'opportunity',
        postedBy: {
          id: 'user-123',
          profile: {
            id: 'profile-123',
            email: '<EMAIL>',
            name: 'Test User',
          },
          student_profile: {
            id: 'student-123',
            first_name: 'Test',
            last_name: 'User',
          },
        },
        countries: [],
        institutions: [],
        postEngagements: [],
        images: [],
        club: null,
      },
    };

    it('should return an opportunity when found', async () => {
      mockOpportunityService.getOpportunityById.mockResolvedValue(
        mockOpportunity,
      );

      const result = await controller.getOpportunityById(
        opportunityId,
        mockUser,
      );

      expect(result).toEqual(mockOpportunity);
      expect(opportunityService.getOpportunityById).toHaveBeenCalledWith(
        opportunityId,
        mockUser,
      );
    });

    it('should throw NotFoundException when opportunity not found', async () => {
      const error = new NotFoundException(
        `Opportunity with ID ${opportunityId} not found`,
      );
      mockOpportunityService.getOpportunityById.mockRejectedValue(error);

      await expect(
        controller.getOpportunityById(opportunityId, mockUser),
      ).rejects.toThrow(error);
      expect(opportunityService.getOpportunityById).toHaveBeenCalledWith(
        opportunityId,
        mockUser,
      );
    });

    it('should handle service errors gracefully', async () => {
      const error = new Error('Database connection failed');
      mockOpportunityService.getOpportunityById.mockRejectedValue(error);

      await expect(
        controller.getOpportunityById(opportunityId, mockUser),
      ).rejects.toThrow(error);
      expect(opportunityService.getOpportunityById).toHaveBeenCalledWith(
        opportunityId,
        mockUser,
      );
    });

    it('should validate UUID format with CustomParseUUIDPipe', () => {
      // This test verifies that the controller uses CustomParseUUIDPipe
      // The actual UUID validation is tested in the pipe's own test file
      const controllerMethod = controller.getOpportunityById;
      expect(controllerMethod).toBeDefined();
    });
  });
});
