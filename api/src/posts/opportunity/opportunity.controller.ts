import {
  Controller,
  Post,
  Put,
  Get,
  Body,
  Logger,
  UseInterceptors,
  UploadedFiles,
  ParseFilePipeBuilder,
  UseGuards,
  Param,
  Query,
} from '@nestjs/common';
import { ZodSerializerDto } from 'nestjs-zod';
import {
  ApiCreatedResponse,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiConsumes,
  ApiBearerAuth,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
} from '@nestjs/swagger';
import { postDto, postQueryParamsDto } from '../dto/post.dto';
import { User } from '@/guards/user.decorator';
import { type User as UserDecoratorType, Post as IPost } from '@/db/schema';
import { UseRoles } from 'nest-access-control';
import { FileInterceptor } from '@nestjs/platform-express';
import { CustomMaxFileSizeValidator } from '@/validators/custom-max-file-size.validator';
import { RoleGuard } from '@/guards/role.guard';
import { CustomFileUploadValidator } from '@/validators/custom-file-type.validator';
import { opportunityDto } from '../dto/opportunity.dto';
import {
  PostRoutes,
  PostServiceMessages,
} from '@app/shared/constants/post.constants';
import { OpportunityService } from './opportunity.service';
import { CustomParseUUIDPipe } from '@/common/pipes/custom-parse-uuid';

@ApiTags('Opportunity')
@Controller({ version: '1', path: 'opportunity' })
export class OpportunityController {
  private readonly logger = new Logger(OpportunityController.name);
  constructor(private opportunityService: OpportunityService) {}

  @Get()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'post', action: 'read', possession: 'any' })
  @ApiOperation({
    summary: 'Get all opportunities',
    description:
      'Retrieve a list of all opportunities with optional filtering and pagination',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Filter opportunities by title or description',
    type: String,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    type: Number,
  })
  @ApiQuery({
    name: 'sort',
    required: false,
    description: 'Field to sort by',
    type: String,
  })
  @ApiQuery({
    name: 'order',
    required: false,
    description: 'Sort order (asc or desc)',
    enum: ['asc', 'desc'],
  })
  @ApiOkResponse({
    description: 'Successfully retrieved all opportunities.',
    type: [opportunityDto],
  })
  async getAllOpportunities(
    @User() user: UserDecoratorType,
    @Query() query: postQueryParamsDto,
  ) {
    try {
      return await this.opportunityService.getAllOpportunities(
        user,
        query as postQueryParamsDto & {
          sort: keyof IPost;
        },
      );
    } catch (error: any) {
      this.logger.error('Error retrieving opportunities', error.stack);
      throw error;
    }
  }

  @Post()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'post', action: 'create', possession: 'any' })
  @ZodSerializerDto(postDto)
  @ApiOperation({
    summary: 'Create new opportunity',
    description:
      'Create a new opportunity with optional attachments and notification settings',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: opportunityDto,
    description: 'Opportunity data to create',
  })
  @ApiCreatedResponse({
    description: 'The record has been successfully created.',
    type: opportunityDto,
  })
  @ApiInternalServerErrorResponse({
    description: 'Failed to create opportunity',
  })
  @UseInterceptors(FileInterceptor('attachments'))
  async createOpportunity(
    @User() user: UserDecoratorType,
    @Body() data: opportunityDto,
    @UploadedFiles(
      new ParseFilePipeBuilder()
        .addValidator(
          new CustomMaxFileSizeValidator({
            maxSize: 2 * 1024 * 1024,
          }),
        )
        .addValidator(new CustomFileUploadValidator({ isRequired: false }))
        .build({ fileIsRequired: false }),
    )
    attachments: Express.Multer.File[] = [],
  ) {
    try {
      return this.opportunityService.createOpportunity(
        data,
        user.id,
        attachments,
      );
    } catch (error: any) {
      this.logger.error('Error creating opportunity: ', error.stack);

      throw error;
    }
  }

  @Put(PostRoutes.UPDATE_OPPORTUNITY)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'post', action: 'update', possession: 'any' })
  @ZodSerializerDto(postDto)
  @ApiOperation({
    summary: 'Update opportunity',
    description: 'Update an existing opportunity by ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Opportunity unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: opportunityDto,
    description: 'Updated opportunity data',
  })
  @ApiCreatedResponse({
    description: 'The record has been successfully updated.',
    type: opportunityDto,
  })
  @ApiNotFoundResponse({
    description: PostServiceMessages.PostNotFound,
  })
  @ApiInternalServerErrorResponse({
    description: 'Failed to update opportunity',
  })
  @UseInterceptors(FileInterceptor('attachments'))
  async updateOpportunity(
    @User() user: UserDecoratorType,
    @Body() data: opportunityDto,
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @UploadedFiles(
      new ParseFilePipeBuilder()
        .addValidator(
          new CustomMaxFileSizeValidator({
            maxSize: 2 * 1024 * 1024,
          }),
        )
        .addValidator(new CustomFileUploadValidator({ isRequired: false }))
        .build({ fileIsRequired: false }),
    )
    attachments: Express.Multer.File[],
  ) {
    try {
      const updatedOpportunity = this.opportunityService.updateOpportunity(
        { data, attachments },
        id,
      );
      return updatedOpportunity;
    } catch (error: any) {
      this.logger.error('Error updating opportunity: ', error.stack);

      // Only log validation errors in development
      if (
        process.env.NODE_ENV === 'development' &&
        error.message &&
        error.message.includes('Invalid format')
      ) {
        this.logger.error('Validation error details:', {
          eligibility: data.eligibility,
          countries: data.countries,
          institutions: data.institutions,
        });
      }

      throw error;
    }
  }

  @Get(PostRoutes.GET_OPPORTUNITY_BY_ID)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'post', action: 'read', possession: 'any' })
  @ApiOperation({
    summary: 'Get opportunity by ID',
    description: 'Retrieve a specific opportunity by its unique identifier',
  })
  @ApiParam({
    name: 'id',
    description: 'Opportunity unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiOkResponse({
    description: 'Successfully retrieved the opportunity.',
    type: opportunityDto,
  })
  @ApiNotFoundResponse({
    description: 'Opportunity with ID not found',
  })
  async getOpportunityById(
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @User() user: UserDecoratorType,
  ) {
    try {
      return await this.opportunityService.getOpportunityById(id, user);
    } catch (error: any) {
      this.logger.error(
        `Error retrieving opportunity with ID ${id}`,
        error.stack,
      );
      throw error;
    }
  }
}
