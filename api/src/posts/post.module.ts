import { Module } from '@nestjs/common';
import { PostService } from './post.service';
import { PostController } from './post.controller';
import { RepositoriesModule } from '@/repositories/repositories.module';
import { UploadModule } from '@/upload/upload.module';
import { OpportunityController } from './opportunity/opportunity.controller';
import { EventsController } from './events/event.controller';
import { OpportunityService } from './opportunity/opportunity.service';
import { OpportunitySelectionService } from './opportunity/opportunity-selection.service';
import { EventService } from './events/event.service';
import { PointSystemRepository } from '@/point-system/repository/point_system.repository';
import { EventEngagementService } from './services/event-engagement.service';
import { EventEngagementController } from './controllers/event-engagement.controller';
import { EventNotificationModule } from '@/event-notification/event-notification.module';
import { SelectionNotificationModule } from '@/notification/selection-notification.module';
import { PostNotificationService } from './services/post-notification.service';
import { EnhancedNotificationModule } from '@app/shared/enhanced-notification/enhanced-notification.module';
import { BullModule } from '@nestjs/bullmq';
import { QueueName } from '@app/shared/queue/queue.constants';
import { PostScheduler } from './post.scheduler';

@Module({
  imports: [
    RepositoriesModule,
    UploadModule,
    EventNotificationModule,
    SelectionNotificationModule,
    EnhancedNotificationModule,
    BullModule.registerQueue({
      name: QueueName.POST_NOTIFICATION,
    }),
    BullModule.registerQueue({
      name: QueueName.UPLOAD,
    }),
  ],
  providers: [
    PostService,
    OpportunityService,
    OpportunitySelectionService,
    EventService,
    PointSystemRepository,
    EventEngagementService,
    PostNotificationService,
    PostScheduler,
  ],
  controllers: [
    PostController,
    OpportunityController,
    EventsController,
    EventEngagementController,
  ],
})
export class PostModule {}
