import { Injectable } from '@nestjs/common';
import {
  PointConfigDto,
  PointRuleDto,
  PointRulesQueryParamsDto,
  PointsConfigQueryParamsDto,
} from './dto';
import { PointSystemRepository } from './repository/point_system.repository';
import {
  PointRule,
  pointRulesSchema,
  pointsLogSchema,
} from '@/db/schema/points_system';
import { and, eq, gte, sql } from 'drizzle-orm';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { periodQueryDto } from '@/common/dto/query-params.dto';

@Injectable()
export class PointSystemService {
  constructor(
    private readonly pointsRepository: PointSystemRepository,
    private readonly drizzle: DrizzleService,
  ) {}

  async createPointConfiguration(data: PointConfigDto): Promise<any> {
    return await this.pointsRepository.createPointConfiguration(data);
  }
  async getPointConfiguration(id: string): Promise<any> {
    return await this.pointsRepository.getPointsConfigById(id);
  }
  async getAllPointConfiguration(
    query: PointsConfigQueryParamsDto,
  ): Promise<any> {
    return await this.pointsRepository.getAllPointConfiguration(query);
  }
  async updatePointConfiguration(
    id: string,
    query: PointConfigDto,
  ): Promise<any> {
    return await this.pointsRepository.updatePointConfiguration(id, query);
  }
  async removePointConfiguration(id: string): Promise<any> {
    return await this.pointsRepository.removePointConfiguration(id);
  }
  async rewardPoints(data: any): Promise<any> {
    return await this.pointsRepository.rewardPoints(data);
  }
  async getAllPoints(
    data: PointsConfigQueryParamsDto,
  ): Promise<{ data: PointConfigDto[]; total: number }> {
    return await this.pointsRepository.getAllPointLogs(data);
  }

  async getAllPointRules(
    data: PointRulesQueryParamsDto,
  ): Promise<{ data: PointRuleDto[]; total: number }> {
    const result = await this.pointsRepository.getAllPointRules(data);
    return result;
  }

  async getStudentPoints(
    student_id: string,
    { period }: periodQueryDto,
  ): Promise<any> {
    const filters = [];
    switch (period) {
      case 'daily':
        filters.push(
          gte(pointsLogSchema.created_at, sql`NOW() - INTERVAL '1 DAY'`),
        );
        break;
      case 'weekly':
        filters.push(
          gte(pointsLogSchema.created_at, sql`NOW() - INTERVAL '1 WEEK'`),
        );
        break;
      case 'monthly':
        filters.push(
          gte(pointsLogSchema.created_at, sql`NOW() - INTERVAL '1 MONTH'`),
        );
        break;
      case 'yearly':
        filters.push(
          gte(pointsLogSchema.created_at, sql`NOW() - INTERVAL '1 YEAR'`),
        );
        break;
      case 'first-quarter':
        filters.push(
          gte(pointsLogSchema.created_at, sql`NOW() - INTERVAL '1 QUARTER'`),
        );
      case 'second-quarter':
        filters.push(
          gte(pointsLogSchema.created_at, sql`NOW() - INTERVAL '2 QUARTER'`),
        );
        break;
      case 'third-quarter':
        filters.push(
          gte(pointsLogSchema.created_at, sql`NOW() - INTERVAL '3 QUARTER'`),
        );
        break;
      case 'fourth-quarter':
        filters.push(
          gte(pointsLogSchema.created_at, sql`NOW() - INTERVAL '4 QUARTER'`),
        );
        break;
      case 'all-time':
        filters.push(
          gte(pointsLogSchema.created_at, sql`NOW() - INTERVAL '5 YEAR'`),
        );
        break;
    }

    const data = await this.drizzle.db
      .select({
        module: pointRulesSchema.module,
        action: pointRulesSchema.action,
        total_points:
          sql<string>`COALESCE(SUM(${pointsLogSchema.points}), 0)`.as(
            'total_points',
          ),
      })
      .from(pointRulesSchema)
      .leftJoin(
        pointsLogSchema,
        eq(pointsLogSchema.point_rule_id, pointRulesSchema.id),
      )
      .where(and(eq(pointsLogSchema.student_id, student_id), ...filters))
      .groupBy(pointRulesSchema.module, pointRulesSchema.action)
      .then((res) => {
        const unlikePoints = res.findIndex((r) => r.action === 'Unlike');
        return res.map((r) => {
          if (r.action === 'Like' && unlikePoints !== -1 && res[unlikePoints]) {
            const unlikes = res[unlikePoints];
            r.total_points = (parseInt(r.total_points) +
              parseInt(unlikes.total_points)) as unknown as string;

            delete res[unlikePoints];
          }

          return {
            module: r.module,
            action: r.action,
            total_points: r.total_points,
          };
        });
      });

    return data;
  }

  async createPointRule(data: PointRule): Promise<PointRuleDto> {
    return await this.pointsRepository.createPointRule(data);
  }
  async updatePointRule(id: string, data: PointRule): Promise<PointRuleDto[]> {
    return await this.pointsRepository.updatePointRule(id, data);
  }
  async removePointRule(id: string): Promise<{ message: string }> {
    return await this.pointsRepository.removePointRule(id);
  }
  async disablePointRule(id: string): Promise<{ message: string }> {
    return await this.pointsRepository.disablePointRule(id);
  }

  async removePointsFromStudent(
    module: string,
    action: string,
    student_id: string,
  ): Promise<void> {
    await this.pointsRepository.removePointsFromStudent(
      module,
      action,
      student_id,
    );
  }

  /**
   * Verifies if points have been awarded to a student for a specific module and action
   * @param studentId Student ID to check
   * @param module Module name
   * @param action Action name
   * @returns boolean indicating if points were already awarded
   */
  async verifyPointsAwarded(
    studentId: string,
    module: string,
    action: string,
  ): Promise<boolean> {
    return await this.pointsRepository.verifyPointsAwarded(
      studentId,
      module,
      action,
    );
  }

  /**
   * Verifies if login points have been awarded to a student today
   * @param studentId Student ID to check
   * @returns boolean indicating if login points were awarded today
   */
  async verifyLoginPointsAwarded(studentId: string): Promise<boolean> {
    return await this.pointsRepository.verifyLoginPointsAwarded(studentId);
  }
}
