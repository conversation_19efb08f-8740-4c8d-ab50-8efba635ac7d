import { Test, TestingModule } from '@nestjs/testing';
import { PointSystemService } from './point_system.service';
import { PointSystemRepository } from './repository/point_system.repository';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';

describe('PointSystemService', () => {
  let service: PointSystemService;
  let repository: PointSystemRepository;

  beforeEach(async () => {
    // Create mock for DrizzleService
    const mockDrizzleService = {
      db: {
        select: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        then: jest.fn().mockResolvedValue([]),
        query: {
          student_profiles: {
            findFirst: jest.fn().mockReturnValue({
              execute: jest.fn().mockResolvedValue(null),
            }),
          },
        },
      },
    };

    // We don't need to mock CacheService directly as it's used by PointSystemRepository

    // Create mock for PointSystemRepository
    const mockPointSystemRepository = {
      createPointConfiguration: jest.fn().mockResolvedValue([]),
      getPointsConfigById: jest.fn().mockResolvedValue([]),
      getAllPointConfiguration: jest
        .fn()
        .mockResolvedValue({ data: [], total: 0 }),
      updatePointConfiguration: jest.fn().mockResolvedValue([]),
      removePointConfiguration: jest
        .fn()
        .mockResolvedValue({ message: 'Success' }),
      rewardPoints: jest.fn().mockResolvedValue([]),
      getAllPointLogs: jest.fn().mockResolvedValue({ data: [], total: 0 }),
      getAllPointRules: jest.fn().mockResolvedValue({ data: [], total: 0 }),
      createPointRule: jest.fn().mockResolvedValue({}),
      updatePointRule: jest.fn().mockResolvedValue([]),
      removePointRule: jest.fn().mockResolvedValue({ message: 'Success' }),
      disablePointRule: jest.fn().mockResolvedValue({ message: 'Success' }),
      removePointsFromStudent: jest.fn().mockResolvedValue(undefined),
      verifyPointsAwarded: jest.fn().mockResolvedValue(false),
      verifyLoginPointsAwarded: jest.fn().mockResolvedValue(false),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PointSystemService,
        {
          provide: PointSystemRepository,
          useValue: mockPointSystemRepository,
        },
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
      ],
    }).compile();

    service = module.get<PointSystemService>(PointSystemService);
    repository = module.get<PointSystemRepository>(PointSystemRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createPointConfiguration', () => {
    it('should create a point configuration', async () => {
      const mockData = {
        point_name: 'Test Points',
        point_value: 10,
        description: 'Test Description',
      };
      const mockResult = [{ id: '1', ...mockData }];

      jest
        .spyOn(repository, 'createPointConfiguration')
        .mockResolvedValue(mockResult);

      const result = await service.createPointConfiguration(mockData);

      expect(repository.createPointConfiguration).toHaveBeenCalledWith(
        mockData,
      );
      expect(result).toEqual(mockResult);
    });
  });

  describe('getPointConfiguration', () => {
    it('should get a point configuration by id', async () => {
      const mockId = '1';
      const mockResult = [
        { id: mockId, point_name: 'Test Points', point_value: 10 },
      ];

      jest
        .spyOn(repository, 'getPointsConfigById')
        .mockResolvedValue(mockResult);

      const result = await service.getPointConfiguration(mockId);

      expect(repository.getPointsConfigById).toHaveBeenCalledWith(mockId);
      expect(result).toEqual(mockResult);
    });
  });

  describe('getAllPointConfiguration', () => {
    it('should get all point configurations', async () => {
      const mockQuery = {
        page: 1,
        limit: 10,
        search: '',
        order: 'asc' as const,
        all: true,
      };
      const mockResult = {
        data: [{ id: '1', point_name: 'Test Points', point_value: 10 }],
        total: 1,
      };

      jest
        .spyOn(repository, 'getAllPointConfiguration')
        .mockResolvedValue(mockResult);

      const result = await service.getAllPointConfiguration(mockQuery);

      expect(repository.getAllPointConfiguration).toHaveBeenCalledWith(
        mockQuery,
      );
      expect(result).toEqual(mockResult);
    });
  });

  describe('updatePointConfiguration', () => {
    it('should update a point configuration', async () => {
      const mockId = '1';
      const mockData = {
        point_name: 'Updated Points',
        point_value: 20,
        description: 'Updated description',
      };
      const mockResult = [{ id: mockId, ...mockData }];

      jest
        .spyOn(repository, 'updatePointConfiguration')
        .mockResolvedValue(mockResult);

      const result = await service.updatePointConfiguration(mockId, mockData);

      expect(repository.updatePointConfiguration).toHaveBeenCalledWith(
        mockId,
        mockData,
      );
      expect(result).toEqual(mockResult);
    });
  });

  describe('removePointConfiguration', () => {
    it('should remove a point configuration', async () => {
      const mockId = '1';
      const mockResult = { message: 'Points deleted successfully' };

      jest
        .spyOn(repository, 'removePointConfiguration')
        .mockResolvedValue(mockResult);

      const result = await service.removePointConfiguration(mockId);

      expect(repository.removePointConfiguration).toHaveBeenCalledWith(mockId);
      expect(result).toEqual(mockResult);
    });
  });

  describe('getAllPointRules', () => {
    it('should get all point rules', async () => {
      const mockQuery = {
        page: 1,
        limit: 10,
        search: '',
        order: 'asc' as const,
        all: true,
      };
      const mockResult = {
        data: [
          {
            id: '1',
            module: 'Test',
            action: 'Create',
            points_config_id: '1',
            frequency: 'once_a_day',
            disabled: false,
            created_at: new Date(),
            updated_at: new Date().toISOString(),
          },
        ],
        total: 1,
      };

      jest.spyOn(repository, 'getAllPointRules').mockResolvedValue(mockResult);

      const result = await service.getAllPointRules(mockQuery);

      expect(repository.getAllPointRules).toHaveBeenCalledWith(mockQuery);
      expect(result).toEqual(mockResult);
    });
  });

  describe('createPointRule', () => {
    it('should create a point rule', async () => {
      const mockData = {
        module: 'Test',
        action: 'Create',
        points_config_id: '1',
        frequency: 'once_a_day',
      };
      const mockResult = { id: '1', ...mockData };

      jest.spyOn(repository, 'createPointRule').mockResolvedValue(mockResult);

      const result = await service.createPointRule(mockData);

      expect(repository.createPointRule).toHaveBeenCalledWith(mockData);
      expect(result).toEqual(mockResult);
    });
  });

  describe('removePointRule', () => {
    it('should remove a point rule', async () => {
      const mockId = '1';
      const mockResult = { message: 'Point rule deleted successfully' };

      jest.spyOn(repository, 'removePointRule').mockResolvedValue(mockResult);

      const result = await service.removePointRule(mockId);

      expect(repository.removePointRule).toHaveBeenCalledWith(mockId);
      expect(result).toEqual(mockResult);
    });
  });
});
