import { Module } from '@nestjs/common';
import { PointSystemService } from './point_system.service';

import {
  PointRewardController,
  PointRulesController,
  PointSystemController,
} from './point_system.controller';
import { PointSystemRepository } from './repository/point_system.repository';

@Module({
  providers: [PointSystemService, PointSystemRepository],
  controllers: [
    PointSystemController,
    PointRulesController,
    PointRewardController,
  ],
  exports: [PointSystemService, PointSystemRepository],
})
export class PointSystemModule {}
