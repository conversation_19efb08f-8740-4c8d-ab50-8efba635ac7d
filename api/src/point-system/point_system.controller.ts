import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { PointSystemService } from './point_system.service';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiOkResponse,
  ApiNoContentResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
} from '@nestjs/swagger';
import { RoleGuard } from '@/guards/role.guard';
import { UseRoles } from 'nest-access-control';
import { type User as IUser } from '@/db/schema';
import { User } from '@/guards/user.decorator';
import {
  PointConfigDto,
  PointRuleDto,
  PointsConfigQueryParamsDto,
  PointsLogDto,
} from './dto';
import { CLIENT_TYPE } from '@/guards/request-validation.decorator';
import { AppClients } from '@app/shared/constants/auth.constants';
import { ZodSerializerDto } from 'nestjs-zod';
import {
  insertPointsConfig,
  insertPointsLogs,
} from '@/db/schema/points_system';
import { PointSystemRoutes } from '@app/shared/constants/points-system.constant';
import { CustomParseUUIDPipe } from '@/common/pipes/custom-parse-uuid';

@ApiTags('Point Systems Configuration')
@Controller({ version: '1', path: 'point-system' })
export class PointSystemController {
  private readonly logger = new Logger(PointSystemController.name);
  constructor(private readonly pointSystemService: PointSystemService) {}

  @Post(PointSystemRoutes.CONFIG)
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'point-config',
    action: 'create',
    possession: 'any',
  })
  @ZodSerializerDto(insertPointsConfig)
  @ApiOperation({
    summary: 'Create point configuration',
    description: 'Create a new point configuration in the system',
  })
  @ApiCreatedResponse({
    description: 'The record has been successfully created.',
    type: PointConfigDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid point configuration data',
  })
  @CLIENT_TYPE(AppClients.WEB)
  async createPointConfiguration(
    @Body() data: PointConfigDto,
    @User(['admin', 'super_admin']) user: IUser,
  ): Promise<PointConfigDto> {
    try {
      return await this.pointSystemService.createPointConfiguration({
        ...data,
        created_by: user.id,
      });
    } catch (error) {
      this.logger.error('Error creating point configuration:', error);
      throw error;
    }
  }

  @Get(PointSystemRoutes.CONFIG_ID)
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'point-config',
    action: 'read',
    possession: 'any',
  })
  @ApiOperation({
    summary: 'Get point configuration by ID',
    description:
      'Retrieve a specific point configuration by its unique identifier',
  })
  @ApiParam({
    name: 'id',
    description: 'Point configuration unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiOkResponse({
    description: 'Successfully retrieved point configuration',
    type: Object,
  })
  @ApiNotFoundResponse({
    description: 'Point configuration not found',
  })
  @CLIENT_TYPE(AppClients.WEB)
  async getPointConfiguration(
    @Param('id', new CustomParseUUIDPipe()) id: string,
  ): Promise<{ data: PointRuleDto[]; total: number }> {
    try {
      return await this.pointSystemService.getPointConfiguration(id);
    } catch (error) {
      this.logger.error('Error fetching point configuration:', error);
      throw new Error('Failed to fetch point configuration');
    }
  }

  @Get(PointSystemRoutes.CONFIG)
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'point-config',
    action: 'read',
    possession: 'any',
  })
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get all configurations',
    description:
      'Retrieve a list of all point configurations with optional filtering and pagination',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Filter configurations by name',
    type: String,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    type: Number,
  })
  @ApiOkResponse({
    description: 'Successfully retrieved all point configurations',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'object',
        },
        total: {
          type: 'number',
        },
      },
    },
  })
  @CLIENT_TYPE(AppClients.WEB)
  async getAllConfiguration(
    @Query() query: PointsConfigQueryParamsDto,
  ): Promise<{ data: PointConfigDto; total: number }> {
    try {
      return await this.pointSystemService.getAllPointConfiguration(query);
    } catch (error) {
      this.logger.error('Error updating point rule:', error);
      throw new Error('Failed to update point rule');
    }
  }

  @Put(PointSystemRoutes.CONFIG_ID)
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'point-config',
    action: 'update',
    possession: 'any',
  })
  @ApiOperation({
    summary: 'Update point configuration',
    description: 'Update an existing point configuration by ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Point configuration unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiOkResponse({
    description: 'Successfully updated point configuration',
    type: PointConfigDto,
  })
  @ApiNotFoundResponse({
    description: 'Point configuration not found',
  })
  @ApiBadRequestResponse({
    description: 'Invalid point configuration data',
  })
  @CLIENT_TYPE(AppClients.WEB)
  async updateConfigurationPoint(
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @Body() query: PointConfigDto,
  ): Promise<any> {
    try {
      return await this.pointSystemService.updatePointConfiguration(id, query);
    } catch (error) {
      this.logger.error('Error updating point configuration:', error);
      throw new Error('Failed to update point configuration');
    }
  }

  @Get(PointSystemRoutes.POINTS)
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'point-reward',
    action: 'read',
    possession: 'any',
  })
  @ApiOperation({
    summary: 'Get all points',
    description:
      'Retrieve a list of all point records with optional filtering and pagination',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Filter points by name',
    type: String,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    type: Number,
  })
  @ApiOkResponse({
    description: 'Successfully retrieved all points',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
          },
        },
        total: {
          type: 'number',
        },
      },
    },
  })
  @CLIENT_TYPE(AppClients.WEB)
  async getPoints(
    @Query() query: PointsConfigQueryParamsDto,
  ): Promise<{ data: PointConfigDto[]; total: number }> {
    try {
      return await this.pointSystemService.getAllPoints(query);
    } catch (error) {
      this.logger.error('Error fetching points:', error);
      throw error;
    }
  }

  @Delete(PointSystemRoutes.CONFIG_ID)
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'point-config',
    action: 'delete',
    possession: 'any',
  })
  @ApiOperation({
    summary: 'Delete point configuration',
    description: 'Delete a point configuration by ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Point configuration unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiNoContentResponse({
    description: 'Successfully deleted point configuration',
  })
  @ApiNotFoundResponse({
    description: 'Point configuration not found',
  })
  @CLIENT_TYPE(AppClients.WEB)
  async removePointConfiguration(
    @Param('id', new CustomParseUUIDPipe()) id: string,
  ): Promise<string> {
    try {
      return this.pointSystemService.removePointConfiguration(id);
    } catch (error) {
      throw error;
    }
  }

  @Get(PointSystemRoutes.VERIFY_POINTS)
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'point-config',
    action: 'read',
    possession: 'any',
  })
  @ApiOperation({
    summary: 'Verify points awarded',
    description:
      'Check if points have been awarded to a student for a specific module and action',
  })
  @ApiParam({
    name: 'studentId',
    description: 'Student unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiParam({
    name: 'module',
    description: 'Module name',
    type: 'string',
    required: true,
  })
  @ApiParam({
    name: 'action',
    description: 'Action name',
    type: 'string',
    required: true,
  })
  @ApiOkResponse({
    description: 'Successfully verified points',
    schema: {
      type: 'object',
      properties: {
        awarded: {
          type: 'boolean',
          example: true,
        },
      },
    },
  })
  @CLIENT_TYPE(AppClients.MOBILE)
  async verifyPointsAwarded(
    @Param('studentId', new CustomParseUUIDPipe()) studentId: string,
    @Param('module') pointModule: string,
    @Param('action') action: string,
  ): Promise<{ awarded: boolean }> {
    try {
      const awarded = await this.pointSystemService.verifyPointsAwarded(
        studentId,
        pointModule,
        action,
      );
      return { awarded };
    } catch (error) {
      this.logger.error('Error verifying points:', error);
      throw error;
    }
  }
}

@ApiTags('Point Rules')
@Controller({ version: '1', path: 'point-rule' })
export class PointRulesController {
  private readonly logger = new Logger(PointRulesController.name);
  constructor(private readonly pointSystemService: PointSystemService) {}

  @Get()
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'point-rule',
    action: 'read',
    possession: 'any',
  })
  @ApiOperation({
    summary: 'Get all point rules',
    description:
      'Retrieve a list of all point rules with optional filtering and pagination',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Filter rules by name',
    type: String,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    type: Number,
  })
  @ApiOkResponse({
    description: 'Successfully retrieved all point rules',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
          },
        },
        total: {
          type: 'number',
        },
      },
    },
  })
  @CLIENT_TYPE(AppClients.WEB)
  async getAllPointRules(
    @Query() query: PointsConfigQueryParamsDto,
  ): Promise<{ data: PointRuleDto[]; total: number }> {
    try {
      return await this.pointSystemService.getAllPointRules(query);
    } catch (error) {
      this.logger.error('Error fetching point rules:', error);
      throw error;
    }
  }

  @Post(PointSystemRoutes.CONFIG)
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'point-rule',
    action: 'create',
    possession: 'any',
  })
  @ApiOperation({
    summary: 'Create point rule',
    description: 'Create a new point rule in the system',
  })
  @ApiCreatedResponse({
    description: 'The point rule has been successfully created',
    type: PointRuleDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid point rule data',
  })
  @CLIENT_TYPE(AppClients.WEB)
  async createPointRules(@Body() data: PointRuleDto): Promise<PointRuleDto> {
    try {
      return await this.pointSystemService.createPointRule(data);
    } catch (error) {
      this.logger.error('Error creating point rule:', error);
      throw error;
    }
  }

  @Put(PointSystemRoutes.ID)
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'point-rule',
    action: 'update',
    possession: 'any',
  })
  @ApiOperation({
    summary: 'Update point rule',
    description: 'Update an existing point rule by ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Point rule unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiOkResponse({
    description: 'Successfully updated point rule',
    type: [PointRuleDto],
  })
  @ApiNotFoundResponse({
    description: 'Point rule not found',
  })
  @CLIENT_TYPE(AppClients.WEB)
  async updatePointRules(
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @Body() data: PointRuleDto,
  ): Promise<PointRuleDto[]> {
    try {
      return await this.pointSystemService.updatePointRule(id, data);
    } catch (error) {
      this.logger.error('Error updating point rule:', error);
      throw error;
    }
  }

  @Delete(PointSystemRoutes.ID)
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'point-rule',
    action: 'delete',
    possession: 'any',
  })
  @ApiOperation({
    summary: 'Delete point rule',
    description: 'Delete a point rule by ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Point rule unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiOkResponse({
    description: 'Successfully deleted point rule',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Point rule deleted successfully',
        },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Point rule not found',
  })
  @CLIENT_TYPE(AppClients.WEB)
  async removePointRule(
    @Param('id', new CustomParseUUIDPipe()) id: string,
  ): Promise<{ message: string }> {
    try {
      return await this.pointSystemService.removePointRule(id);
    } catch (error) {
      this.logger.error('failed to remove point rule:', error);
      throw error;
    }
  }

  @Put(PointSystemRoutes.ID)
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'point-rule',
    action: 'update',
    possession: 'any',
  })
  @ApiOperation({
    summary: 'Disable point rule',
    description: 'Disable an existing point rule by ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Point rule unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiOkResponse({
    description: 'Successfully disabled point rule',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Point rule disabled successfully',
        },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Point rule not found',
  })
  @CLIENT_TYPE(AppClients.WEB)
  async disablePointRule(
    @Param('id', new CustomParseUUIDPipe()) id: string,
  ): Promise<{ message: string }> {
    try {
      return await this.pointSystemService.disablePointRule(id);
    } catch (error) {
      this.logger.error('Error disabling point rule:', error);
      throw error;
    }
  }
}

@ApiTags('Point Rewards')
@Controller({ version: '1', path: 'point-reward' })
export class PointRewardController {
  private readonly logger = new Logger(PointRewardController.name);
  constructor(private readonly pointSystemService: PointSystemService) {}

  @Post(PointSystemRoutes.REWARD)
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'point-reward',
    action: 'create',
    possession: 'any',
  })
  @ZodSerializerDto(insertPointsLogs)
  @ApiOperation({
    summary: 'Reward points',
    description: 'Award points to a student based on specific rules',
  })
  @ApiCreatedResponse({
    description: 'Points have been successfully rewarded',
    type: PointsLogDto,
  })
  @ApiBearerAuth()
  @ApiBadRequestResponse({
    description: 'Invalid points data',
  })
  @CLIENT_TYPE(AppClients.WEB)
  async rewardPoints(@Body() data: PointsLogDto): Promise<PointsLogDto> {
    try {
      return await this.pointSystemService.rewardPoints(data);
    } catch (error) {
      this.logger.error('Error fetching point configuration:', error);
      throw error;
    }
  }

  @Delete(PointSystemRoutes.REWARD)
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'point-reward',
    action: 'delete',
    possession: 'any',
  })
  @ApiOperation({
    summary: 'Remove points from student',
    description:
      'Remove awarded points from a student for a specific module and action',
  })
  @ApiParam({
    name: 'module',
    description: 'Module name',
    type: 'string',
    required: true,
  })
  @ApiParam({
    name: 'action',
    description: 'Action name',
    type: 'string',
    required: true,
  })
  @ApiParam({
    name: 'student_id',
    description: 'Student unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiNoContentResponse({
    description: 'Successfully removed points from student',
  })
  @ApiNotFoundResponse({
    description: 'No points found for this module/action/student combination',
  })
  @CLIENT_TYPE(AppClients.WEB)
  async removePointsFromStudent(
    @Param('module') module: string,
    @Param('action') action: string,
    @Param('student_id', new CustomParseUUIDPipe()) student_id: string,
  ): Promise<void> {
    try {
      await this.pointSystemService.removePointsFromStudent(
        module,
        action,
        student_id,
      );
    } catch (error) {
      this.logger.error('failed to remove points from student:', error);
      throw error;
    }
  }
}
