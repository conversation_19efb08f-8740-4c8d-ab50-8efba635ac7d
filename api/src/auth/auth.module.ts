import { Module } from '@nestjs/common';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { JwtHelperModule } from 'src/jwt-helper/jwt-helper.module';
import { EmailModule } from 'src/mail/email.module';
import { ScheduleModule } from '@nestjs/schedule';

import { PointSystemRepository } from '@/point-system/repository/point_system.repository';
import { StudentProfileService } from '@/student_profile/student_profile.service';
import { NotificationModule } from '@app/shared/notification/notification.module';
import { McqModule } from '@/mcq/mcq.module';

@Module({
  imports: [
    JwtHelperModule,
    EmailModule,
    ScheduleModule.forRoot(),
    NotificationModule,
    McqModule,
  ],
  controllers: [AuthController],
  providers: [AuthService, PointSystemRepository, StudentProfileService],
  exports: [AuthService],
})
export class AuthModule {}
