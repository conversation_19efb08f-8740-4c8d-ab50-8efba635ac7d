import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  UnauthorizedException,
  Logger,
  HttpStatus,
  NotFoundException,
} from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import {
  LoginInputDto,
  UserDto,
  UserQueryParams,
  ApproveWaitingListResponse,
} from '@/auth/dto/auth.dto';
import { JwtHelperService } from '@/jwt-helper/jwt-helper.service';
import { Request } from 'express';
import { user_roles, user_states, users } from 'src/db/schema/users';
import {
  and,
  eq,
  getTableColumns,
  desc,
  asc,
  ilike,
  or,
  inArray,
  not,
  sql,
} from 'drizzle-orm';
import {
  AuthControllerMessage,
  AuthServiceMessage,
} from '@app/shared/constants/auth.message';
import {
  AppClients,
  AuthDurations,
} from '@app/shared/constants/auth.constants';
import { EmailService } from 'src/mail/email.service';
import { EnvConfig } from '@app/shared/dto/env-config.dto';
import { otpTypes, token } from 'src/db/schema/token';
import {
  organisations,
  student_profiles,
  StudentProfile,
  questionsSchema,
} from '@/db/schema';
import { PointConstant } from '@app/shared/constants/points-system.constant';
import { PointSystemRepository } from '@/point-system/repository/point_system.repository';
import { StudentProfileService } from '@/student_profile/student_profile.service';
import { NotificationService } from '@app/shared/notification/notification.service';
import { validateAcademicEmail } from '@/util/validate-academic-email';
import { CacheService } from '@app/shared/redis/cache.service';
import {
  AUTH_CACHE_PREFIX,
  invalidateWaitingListCaches,
} from './utils/cache.utils';
import { normalizeEmail } from '@/util/normalize-email';

@Injectable()
export class AuthService {
  constructor(
    private readonly drizzle: DrizzleService,
    private readonly jwtHelperService: JwtHelperService,
    private readonly emailService: EmailService,
    private readonly envConfig: EnvConfig,
    private readonly pointSystemRepository: PointSystemRepository,
    private readonly studentProfileService: StudentProfileService,
    private readonly notificationService: NotificationService,
    private readonly cacheService: CacheService,
  ) {}
  private readonly logger = new Logger(AuthService.name);

  async login({ email, redirectUrl }: LoginInputDto, req: Request) {
    let user: UserDto | undefined;
    // For the web client, delegate to webLogin
    if (req.headers['x-client-type'] == AppClients.WEB) {
      return this.webLogin(email, redirectUrl);
    }
    const Email = normalizeEmail(email);

    // TODO Uncomment this block going to production
    // if (!validateAcademicEmail(email)) {
    // this.logger.warn(`Invalid student email attempt: ${email}`);
    // throw new BadRequestException(AuthServiceMessage.InvalidStudentEmail);
    // }
    // Add a condition to exclude soft-deleted users:
    user = await this.drizzle.db.query.users.findFirst({
      where: and(
        eq(users.email, Email),
        eq(users.deleted, false),
        not(eq(users.state, user_states.PENDING)),
      ),
    });

    if (
      user &&
      user.role !== user_roles.STUDENT &&
      user.role !== user_roles.STUDENT_ADMIN
    )
      throw new UnauthorizedException();
    if (!user) {
      // A new signup will create a fresh user record even if a record previously existed and was soft-deleted.
      // Use a transaction to ensure database consistency
      this.logger.log(`Adding new user to wait-list: ${Email}`);

      try {
        // Start a transaction
        [user] = await this.drizzle.db
          .insert(users)
          .values({
            email: Email,
            state: user_states.PENDING,
          })
          .returning();

        this.logger.log(
          `Successfully added user to wait-list: ${email}, ID: ${user?.id}`,
        );

        // Invalidate all waiting list caches to ensure the new user is reflected in counts
        try {
          await invalidateWaitingListCaches(this.cacheService);
          this.logger.log('Successfully invalidated waiting list caches');
        } catch (cacheError) {
          // Log but don't fail the operation if cache invalidation fails
          this.logger.error(
            'Failed to invalidate waiting list caches',
            cacheError,
          );
        }

        // Emit SSE event for wish list join
        this.notificationService.emitWishListJoined({
          userId: user?.id,
          email: user?.email,
          joinedAt: new Date().toISOString(),
        });

        // Send email notifications to admin users
        if (user?.email) {
          this.sendAdminWaitingListNotifications(user.email);
        }

        return {
          message: AuthControllerMessage.JOINED_WAITING_LIST,
          statusCode: HttpStatus.OK,
        };
      } catch (error) {
        this.logger.error(`Failed to add user to wait-list: ${email}`, error);
        throw error;
      }
    }
    if (user.state === user_states.PENDING) {
      return {
        message: AuthControllerMessage.ALREADY_ON_WAITING_LIST,
        statusCode: HttpStatus.OK,
      };
    }

    const { otpHash, otp: otpString } = this.jwtHelperService.generateOtp();
    await this.drizzle.db
      .insert(token)
      .values({
        user_id: user?.id,
        token: otpHash,
        type: otpTypes.OTP,
        expiresAt: new Date(Date.now() + AuthDurations.TEN_MINUTES),
      })
      .onConflictDoUpdate({
        target: [token.user_id],
        set: {
          token: otpHash,
          expiresAt: new Date(Date.now() + AuthDurations.TEN_MINUTES),
        },
      })
      .returning();
    await this.emailService.sendOtp(email, otpString);
  }

  async webLogin(email: string, redirectUrl?: string) {
    // Also filter out soft-deleted users for web login
    const user: UserDto | undefined =
      await this.drizzle.db.query.users.findFirst({
        where: and(eq(users.email, email), eq(users.deleted, false)),
      });
    if (!user) {
      throw new BadRequestException(AuthServiceMessage.UserNotFound);
    }
    if (user.role === user_roles.STUDENT) {
      throw new ForbiddenException(AuthServiceMessage.AccessDenied);
    }
    const accessToken = await this.jwtHelperService.generateAccessToken({
      userId: user.id,
      expiresIn: this.envConfig.MAGIC_LINK_EXPIRY,
    });

    await this.drizzle.db
      .insert(token)
      .values({
        user_id: user.id,
        token: accessToken,
        type: otpTypes.MAGIC_LINK,
        expiresAt: new Date(Date.now() + this.envConfig.OTP_EXPIRY_TIME),
      })
      .onConflictDoUpdate({
        target: [token.user_id],
        set: {
          token: accessToken,
          expiresAt: new Date(Date.now() + this.envConfig.OTP_EXPIRY_TIME),
        },
      });
    const magicLink = redirectUrl
      ? `${process.env.FRONTEND_URL}/auth/verify-magic-link?token=${accessToken}&redirectUrl=${redirectUrl}`
      : `${process.env.FRONTEND_URL}/auth/verify-magic-link?token=${accessToken}`;

    await this.emailService.sendEmailSignInLink(email, magicLink);

    return email;
  }

  async verifyOtp(otpString: string): Promise<VerifyOtpResponse | undefined> {
    const hashedOtp = this.jwtHelperService.hashOtp(otpString);
    const storedOtp = await this.drizzle.db.query.token.findFirst({
      where: eq(token.token, hashedOtp),
      with: {
        user: {
          with: {
            student_profile: true,
          },
        },
      },
    });
    if (!storedOtp) {
      throw new BadRequestException(AuthServiceMessage.InvalidOtp);
    }

    if (new Date(storedOtp.expiresAt) < new Date()) {
      throw new BadRequestException(AuthServiceMessage.ExpiredOtp);
    }
    if (!storedOtp.user_id) return;
    const tokens = await this.jwtHelperService.generateAuthTokens(
      storedOtp.user_id,
    );

    const cookieOptions = this.jwtHelperService.getCookieOptions();

    if (storedOtp.user.state !== user_states.ACTIVE) {
      await this.drizzle.db
        .update(users)
        .set({ state: user_states.VERIFIED })
        .where(eq(users.id, storedOtp.user_id));
    }
    const studentId =
      await this.studentProfileService.getStudentProfileIdByUserId(
        storedOtp.user_id,
      );
    // Award points for completing the OTP verification
    if (studentId) {
      const pointsAlreadyAwarded =
        await this.pointSystemRepository.verifyLoginPointsAwarded(studentId);

      if (!pointsAlreadyAwarded) {
        await this.pointSystemRepository.awardPointsToStudent(
          PointConstant.MODULES.USER,
          PointConstant.ACTIONS.LOGIN,
          studentId,
        );
      }
    }
    return {
      ...tokens,
      user: {
        id: storedOtp.user?.id,
        email: storedOtp.user?.email,
        state: storedOtp.user?.state,
        profile_pic_url: storedOtp.user?.profile_pic_url,
        student_profile: storedOtp.user?.student_profile,
        deleted: storedOtp.user?.deleted,
      },
      cookieOptions,
    } as never;
  }

  async verifyMagicLink(magicToken: string) {
    const storedMagicToken = await this.drizzle.db.query.token.findFirst({
      where: eq(token.token, magicToken),
    });

    if (!storedMagicToken) {
      throw new BadRequestException(AuthServiceMessage.ExpiredMagicLink);
    }

    const payload = await this.jwtHelperService.verifyAccessToken({
      token: magicToken,
    });

    if (!payload) {
      throw new ForbiddenException();
    }

    const [user] = await this.drizzle.db
      .select({
        ...getTableColumns(users),
        admin_profile: {
          id: organisations.id,
          name: organisations.name,
          address: organisations.address,
          user_id: organisations.user_id,
        },
        student_profile: {
          id: student_profiles.id,
          first_name: student_profiles.first_name,
          last_name: student_profiles.last_name,
          user_id: student_profiles.user_id,
        },
      })
      .from(users)
      .where(eq(users.id, payload.userId))
      .leftJoin(organisations, eq(organisations.user_id, users.id))
      .leftJoin(student_profiles, eq(student_profiles.user_id, users.id));

    if (!user) {
      throw new ForbiddenException();
    }
    // await this.drizzle.db.delete(token).where(eq(token.token, magicToken));
    await this.drizzle.db
      .update(users)
      .set({ state: 'active' })
      .where(eq(users.id, user.id));

    const tokens = await this.jwtHelperService.generateAuthTokens(user.id);

    const cookieOptions = this.jwtHelperService.getCookieOptions();

    return {
      ...tokens,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        state: user.state,
        profile_pic_url: user.profile_pic_url,
        admin_profile: user.admin_profile,
        student_profile: user.student_profile,
      },
      cookieOptions,
    };
  }

  async refreshAccessToken(
    req: Request,
    refreshToken: string,
  ): Promise<string> {
    const accessToken = await this.jwtHelperService.refreshAccessToken(
      req,
      refreshToken,
    );

    if (!accessToken) {
      throw new ForbiddenException();
    }
    return accessToken;
  }
  async deactivateAccount(userId: string) {
    const result = await this.drizzle.db
      .update(users)
      .set({
        deleted: true,
        deleted_at: new Date().toISOString(),
        state: user_states.DISABLED,
      })
      .where(eq(users.id, userId))
      .execute();

    this.logger.warn('Deactivation result:', result);
    if (!result?.rowCount) {
      throw new Error('No user found to deactivate');
    }
    this.logger.log('Account deactivated successfully for user');
  }

  async deleteOwnAccount(userId: string, confirmationText: string) {
    this.logger.log(`Starting self-deletion process for user: ${userId}`);

    // Validate confirmation text - accept both "DELETE" and "delete my account" (case-insensitive)
    const normalizedText = confirmationText.toLowerCase();
    const validTexts = ['delete', 'delete my account'];

    if (!validTexts.includes(normalizedText)) {
      throw new BadRequestException(
        'Invalid confirmation text. Must type exactly: "DELETE" or "delete my account"',
      );
    }

    try {
      // First, check if user exists and get their information
      const userToDelete = await this.drizzle.db.query.users.findFirst({
        where: eq(users.id, userId),
        with: {
          student_profile: true,
          profile: true,
        },
      });

      if (!userToDelete) {
        throw new NotFoundException(`User account not found`);
      }

      // Check for questions created by this user (RESTRICT constraint)
      const questionsCount = await this.drizzle.db
        .select({ count: sql`COUNT(*)` })
        .from(questionsSchema)
        .where(eq(questionsSchema.created_by, userId));

      if (Number(questionsCount[0]?.count || 0) > 0) {
        throw new BadRequestException(
          'Cannot delete account: You have created questions that are referenced by other records. Please contact support for assistance.',
        );
      }

      // Perform deletion in a transaction
      const deletionResult = await this.drizzle.db.transaction(async (tx) => {
        this.logger.log(`Deleting user ${userId} and all related data...`);

        // The following will be automatically handled by CASCADE constraints:
        // - student_profiles (and related points_logs, student_club_memberships, students_careers, post_engagements, raffle_participants, raffle_winners, quiz_score)
        // - token
        // - posts (and related events, opportunities, post_countries, post_institutions, post_images)
        // - notification_preferences
        // - device_tokens

        // Delete the user (this will cascade to all related tables)
        const deletedUser = await tx
          .delete(users)
          .where(eq(users.id, userId))
          .returning({
            id: users.id,
            email: users.email,
            role: users.role,
          });

        if (!deletedUser.length) {
          throw new Error('Failed to delete user account');
        }

        this.logger.log(
          `Successfully deleted user ${userId} and all related data`,
        );

        return deletedUser[0];
      });

      // Invalidate relevant caches
      try {
        await Promise.all([
          invalidateWaitingListCaches(this.cacheService),
          // Invalidate user-specific caches
          this.cacheService.del(`user:${userId}`),
          this.cacheService.invalidatePattern(`*:${userId}:*`),
        ]);
        this.logger.log(
          'Successfully invalidated caches after account deletion',
        );
      } catch (cacheError) {
        this.logger.error(
          'Failed to invalidate caches after account deletion',
          cacheError,
        );
      }

      this.logger.log(
        `Account deletion completed successfully for: ${deletionResult?.email}`,
      );

      return {
        message:
          'Your account and all related data have been permanently deleted',
        deletedUser: deletionResult
          ? {
              id: deletionResult.id,
              email: deletionResult.email,
              role: deletionResult.role,
            }
          : null,
      };
    } catch (error: any) {
      this.logger.error(
        `Failed to delete account for user ${userId}:`,
        error.stack,
      );
      throw error;
    }
  }

  async deleteUser(userId: string, performedBy: string) {
    this.logger.log(
      `Starting admin deletion process for user: ${userId} (performed by: ${performedBy})`,
    );

    try {
      // First, check if user exists and get their information
      const userToDelete = await this.drizzle.db.query.users.findFirst({
        where: eq(users.id, userId),
        with: {
          student_profile: true,
          profile: true,
        },
      });

      if (!userToDelete) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Check for questions created by this user (RESTRICT constraint)
      const questionsCount = await this.drizzle.db
        .select({ count: sql`COUNT(*)` })
        .from(questionsSchema)
        .where(eq(questionsSchema.created_by, userId));

      if (Number(questionsCount[0]?.count || 0) > 0) {
        throw new BadRequestException(
          'Cannot delete user: User has created questions that are referenced by other records. Please reassign or delete the questions first.',
        );
      }

      // Perform deletion in a transaction
      const deletionResult = await this.drizzle.db.transaction(async (tx) => {
        this.logger.log(`Deleting user ${userId} and all related data...`);

        // The following will be automatically handled by CASCADE constraints:
        // - student_profiles (and related points_logs, student_club_memberships, students_careers, post_engagements, raffle_participants, raffle_winners, quiz_score)
        // - token
        // - posts (and related events, opportunities, post_countries, post_institutions, post_images)
        // - notification_preferences
        // - device_tokens

        // The following will be automatically handled by SET NULL constraints:
        // - student_clubs (club_admin, created_by fields will be set to null)

        // Delete the user (this will cascade to all related tables)
        const deletedUser = await tx
          .delete(users)
          .where(eq(users.id, userId))
          .returning({
            id: users.id,
            email: users.email,
            role: users.role,
          });

        if (!deletedUser.length) {
          throw new Error('Failed to delete user');
        }

        return deletedUser[0];
      });

      // Invalidate relevant caches
      try {
        await Promise.all([
          invalidateWaitingListCaches(this.cacheService),
          // Invalidate user-specific caches
          this.cacheService.del(`user:${userId}`),
          this.cacheService.invalidatePattern(`*:${userId}:*`),
          // Add other cache invalidations as needed
        ]);
        this.logger.log('Successfully invalidated caches after user deletion');
      } catch (cacheError) {
        this.logger.error(
          'Failed to invalidate caches after user deletion',
          cacheError,
        );
      }

      this.logger.log(
        `User deletion completed successfully for: ${deletionResult?.email}`,
      );

      return {
        message: 'User and all related data deleted successfully',
        deletedUser: deletionResult
          ? {
              id: deletionResult.id,
              email: deletionResult.email,
              role: deletionResult.role,
            }
          : null,
      };
    } catch (error: any) {
      this.logger.error(`Failed to delete user ${userId}:`, error.stack);
      throw error;
    }
  }

  async approveWaitingList(
    userIds: string[],
  ): Promise<ApproveWaitingListResponse> {
    try {
      const result = await this.drizzle.db
        .update(users)
        .set({
          state: user_states.INACTIVE,
          updated_at: new Date().toISOString(),
        })
        .where(
          and(
            inArray(users.id, userIds),
            eq(users.deleted, false),
            eq(users.state, user_states.PENDING),
          ),
        )
        .returning({
          id: users.id,
          email: users.email,
          state: users.state,
        });

      if (!result.length) {
        throw new NotFoundException('No valid users found in the waiting list');
      }

      // Send emails individually
      for (const user of result) {
        await this.emailService.waitingListNotification({ email: user.email });
      }

      // Invalidate waiting list caches
      try {
        await invalidateWaitingListCaches(this.cacheService);
        this.logger.log(
          'Successfully invalidated waiting list caches after approval',
        );
      } catch (cacheError) {
        this.logger.error(
          'Failed to invalidate waiting list caches after approval',
          cacheError,
        );
      }

      return {
        total: result.length,
        data: result,
      };
    } catch (error) {
      this.logger.error('Batch approval failed:', error);
      throw error;
    }
  }
  async getWaitingList(filters: UserQueryParams): Promise<any> {
    // Provide sensible defaults
    const { limit, sort, page, order, search } = filters;
    const offset = (page - 1) * limit;

    this.logger.log(
      `Fetching waiting list: page=${page}, limit=${limit}, sort=${sort}, order=${order}`,
    );

    // Skip cache for search queries
    const useCache = !search?.trim();

    // Generate cache key if we're using cache
    let cacheKey: string | null = null;
    if (useCache) {
      cacheKey = this.cacheService.generateKey(
        [`waiting-list:${page}:${limit}:${sort}:${order}`],
        AUTH_CACHE_PREFIX,
      );

      // Try to get from cache first
      if (cacheKey) {
        const cachedData = await this.cacheService.get(cacheKey);
        if (cachedData) {
          this.logger.log(`Retrieved waiting list from cache: ${cacheKey}`);
          return cachedData;
        }
      }
    }

    // Build conditions
    const baseConditions = [
      eq(users.deleted, false),
      eq(users.state, user_states.PENDING),
    ];

    // Add search if present
    const searchConditions = search?.trim()
      ? [or(ilike(users.email, `%${search}%`))]
      : [];
    const conditions = and(...baseConditions, ...searchConditions);

    try {
      // First, get the total count directly to ensure it's up-to-date
      const countResult = await this.drizzle.db
        .select({ count: sql`COUNT(*)` })
        .from(users)
        .where(conditions);

      const total = Number(countResult[0]?.count || 0);
      this.logger.log(`Found ${total} users in waiting list`);

      // Then get the paginated list
      const waitingList = await this.drizzle.db
        .select()
        .from(users)
        .where(conditions)
        .limit(limit)
        .offset(offset)
        .orderBy(
          order === 'asc'
            ? asc(users[sort as keyof typeof users] as any)
            : desc(users[sort as keyof typeof users] as any),
        );

      this.logger.log(`Retrieved ${waitingList.length} users for current page`);

      const result = {
        data: waitingList,
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
          hasNextPage: offset + limit < total,
          hasPreviousPage: page > 1,
        },
      };

      // Cache the result if we're using cache
      if (useCache && cacheKey) {
        try {
          await this.cacheService.set(cacheKey, result, 300); // Cache for 5 minutes
          this.logger.log(`Cached waiting list data: ${cacheKey}`);
        } catch (cacheError) {
          this.logger.error('Failed to cache waiting list data', cacheError);
        }
      }

      return result;
    } catch (error) {
      this.logger.error('Error fetching waiting list:', error);
      throw error;
    }
  }

  async disApproveWaitingList(userIds: string[]): Promise<any> {
    // First, get the user emails before deleting them
    const usersToDelete = await this.drizzle.db
      .select({
        id: users.id,
        email: users.email,
      })
      .from(users)
      .where(and(inArray(users.id, userIds), eq(users.deleted, false)));

    if (!usersToDelete.length) {
      throw new NotFoundException('No valid users found to disapprove');
    }

    // Delete the users from the database
    const result = await this.drizzle.db
      .delete(users)
      .where(and(inArray(users.id, userIds), eq(users.deleted, false)))
      .returning({
        id: users.id,
        email: users.email,
      });

    if (!result.length) {
      throw new NotFoundException(
        'Failed to delete users from the waiting list',
      );
    }

    // Send rejection emails to the users
    for (const user of usersToDelete) {
      try {
        await this.emailService.disApproveWaitingListNotification({
          email: user.email,
        });
        this.logger.log(`Sent rejection email to ${user.email}`);
      } catch (error) {
        this.logger.error(
          `Failed to send rejection email to ${user.email}`,
          error,
        );
      }
    }

    // Invalidate waiting list caches
    try {
      await invalidateWaitingListCaches(this.cacheService);
      this.logger.log(
        'Successfully invalidated waiting list caches after disapproval',
      );
    } catch (cacheError) {
      this.logger.error(
        'Failed to invalidate waiting list caches after disapproval',
        cacheError,
      );
    }

    return {
      total: result.length,
      data: result.map((user) => ({
        ...user,
        state: 'deleted', // Include state for backward compatibility
      })),
    };
  }

  /**
   * Send notifications to admin users when a student is added to the waiting list
   * @param studentEmail The email of the student who joined the waiting list
   */
  /**
   * Check for pending users with non-academic emails and send reminders
   * Runs every Thursday at 12:00 PM
   */
  @Cron('0 12 * * 4')
  async checkPendingNonAcademicEmails() {
    this.logger.log(
      'Running weekly Thursday check for pending users with non-academic emails',
    );

    try {
      // Process the job directly instead of using a queue
      const result = await this.notifyPendingNonAcademicUsers();

      this.logger.log(
        `Weekly academic email reminder job completed: ${result.count} emails sent, ${result.failures} failures`,
      );
    } catch (error: any) {
      this.logger.error(
        'Error in weekly scheduled academic email reminder job',
        error?.stack,
      );
    }
  }

  /**
   * Send notifications to pending users with non-academic emails
   * @returns Object with count of notifications sent and failures
   */
  async notifyPendingNonAcademicUsers() {
    // Track metrics
    const metrics = {
      count: 0,
      failures: 0,
      startTime: Date.now(),
    };

    try {
      const pendingUsers = await this.drizzle.db
        .select()
        .from(users)
        .where(
          and(eq(users.state, user_states.PENDING), eq(users.deleted, false)),
        );

      if (pendingUsers.length === 0) {
        return {
          message: 'No pending users found',
          count: 0,
          failures: 0,
          duration: Date.now() - metrics.startTime,
        };
      }

      const nonAcademicEmailUsers = pendingUsers.filter(
        (user) => !validateAcademicEmail(user.email),
      );

      if (nonAcademicEmailUsers.length === 0) {
        return {
          message: 'No pending users with non-academic emails found',
          count: 0,
          failures: 0,
          duration: Date.now() - metrics.startTime,
        };
      }

      // Calculate rejection date - 7 days from now
      const rejectionDate = new Date();
      rejectionDate.setDate(rejectionDate.getDate() + 14);
      const formattedRejectionDate = rejectionDate.toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'long',
        day: 'numeric',
        year: 'numeric',
      });

      const batchSize = 20;
      const batches = Math.ceil(nonAcademicEmailUsers.length / batchSize);

      for (let i = 0; i < batches; i++) {
        const batchStart = i * batchSize;
        const batchEnd = Math.min(
          (i + 1) * batchSize,
          nonAcademicEmailUsers.length,
        );
        const userBatch = nonAcademicEmailUsers.slice(batchStart, batchEnd);

        // Process each user in the batch
        const emailPromises = userBatch.map(async (user) => {
          try {
            await this.emailService.sendCustomEmail({
              email: user.email,
              subject: 'Action Required: Academic Email Needed',
              template: 'notification',
              context: {
                greeting: `Hello,`,
                message: `
                  <p>We noticed that you signed up using a non-academic email address (${user.email}).</p>
                  <p><strong>Important:</strong> Our platform is exclusively for students and staff with academic email addresses.</p>
                  <p>Please sign up again using your academic email address (ending with .edu, .ac, or other academic domain) to continue with the registration process.</p>
                  <p>Your current pending account will be automatically rejected on <strong>${formattedRejectionDate}</strong> (in 7 days) if not updated.</p>
                  <p>Academic emails typically end with domains like .edu, .ac.uk, .edu.au, .ac, or other university domains.</p>
                `,
                appName: this.envConfig.APP_NAME,
              },
            });

            metrics.count++;
            return { success: true, email: user.email };
          } catch (error: any) {
            metrics.failures++;
            this.logger.error(
              `Failed to send academic email reminder to ${user.email}:`,
              error?.stack,
            );
            return { success: false, email: user.email, error };
          }
        });

        // Wait for all emails in this batch to be processed
        await Promise.all(emailPromises);

        // Small delay between batches to avoid rate limiting
        if (i < batches - 1) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }

      const duration = Date.now() - metrics.startTime;
      this.logger.log(
        `Sent academic email reminders to ${metrics.count} users (${metrics.failures} failures) in ${duration}ms`,
      );

      return {
        message: `Sent academic email reminders to ${metrics.count} users`,
        count: metrics.count,
        failures: metrics.failures,
        duration,
      };
    } catch (error: any) {
      this.logger.error('Error sending academic email reminders', error?.stack);
      throw new Error(
        `Failed to send academic email reminders: ${error.message}`,
      );
    }
  }

  private async sendAdminWaitingListNotifications(
    studentEmail: string,
  ): Promise<void> {
    try {
      if (!studentEmail) {
        this.logger.warn(
          'Cannot send admin notifications: student email is missing',
        );
        return;
      }
      //TODO Sending email to a specific admin user
      // Get all admin users
      // const adminUsers = await this.drizzle.db
      // .select({
      // id: users.id,
      // email: users.email,
      // })
      // .from(users)
      // .where(
      // and(
      // eq(users.deleted, false),
      // eq(users.state, user_states.ACTIVE),
      // or(eq(users.role, user_roles.ADMIN)),
      // ),
      // );

      // if (!adminUsers.length) {
      // this.logger.warn(
      // 'No admin users found to notify about waiting list join',
      // );
      // return;
      // }
      //TODO END OF TODO

      // const adminEmails = adminUsers.map((admin) => admin.email);

      // Send notifications to admin users
      await this.emailService.sendWaitingListAdminNotification({
        studentEmail,
        adminEmails: this.envConfig.ADMIN_EMAIL,
      });
    } catch (error: any) {
      // Log error but don't throw - this is a notification and shouldn't block the main flow
      this.logger.error(
        `Failed to send admin notifications for student ${studentEmail}: ${error?.message}`,
        error?.stack,
      );
    }
  }
}
// auth.service.interface.ts
export interface VerifyOtpResponse {
  accessToken: string;
  refreshToken: string;
  user: {
    id: string | undefined;
    email: string | undefined;
    state: string | undefined;
    profile_pic_url: string | null;
    student_profile: StudentProfile | undefined;
    deleted: boolean | undefined;
  };
  cookieOptions: any;
}
