import { Module } from '@nestjs/common';
import { EnvConfig, envConfigSchema } from '@app/shared/dto/env-config.dto';
import { dotenvLoader, TypedConfigModule } from 'nest-typed-config';
import { LoggerModule } from 'nestjs-pino';
import { DatabaseModule } from '@app/shared/drizzle/drizzle.module';
import { NotificationSeedService } from './notification-seed.service';
import { NotificationSeedCommand } from './notification-seed.command';
import { EventNotificationSeedService } from './event-notification-seed.service';
import { EventNotificationSeedCommand } from './event-notification-seed.command';
import { AllNotificationsSeedCommand } from './all-notifications-seed.command';
import { EventNotificationSeed } from '@/db/seeds/event-notification.seed';
import { SelectionNotificationSeedService } from './selection-notification-seed.service';
import { SelectionNotificationSeedCommand } from './selection-notification-seed.command';
import { validateRedisConfigForEnvironment } from '@app/shared/redis/redis-config.validator';
import { EnhancedNotificationModule } from '@app/shared/enhanced-notification/enhanced-notification.module';
import { RedisModule } from '@app/shared/redis/redis.module';
import { CacheModule } from '@app/shared/cache/cache.module';
import { ConfigModule } from '@nestjs/config';
import { AccessControlModule } from 'nest-access-control';
import { RBAC_ROLES } from '@/auth/app.roles';

import * as dotenv from 'dotenv';

// Ensure .env is loaded
dotenv.config();

@Module({
  imports: [
    TypedConfigModule.forRoot({
      isGlobal: true,
      load: dotenvLoader(),
      schema: EnvConfig,
      validate: (config) => envConfigSchema.parse(config),
    }),
    LoggerModule.forRootAsync({
      imports: [TypedConfigModule],
      inject: [EnvConfig],
      useFactory: () => ({
        pinoHttp: {
          customProps: () => ({
            context: 'HTTP',
          }),
        },
      }),
    }),
    DatabaseModule.registerAsync({
      inject: [EnvConfig],
      imports: [TypedConfigModule],
      useFactory: (envCongDto: EnvConfig) => {
        return {
          connectionString: envCongDto.DATABASE_URL,
          max: 1,
          debug: true,
        };
      },
    }),
    // Register ConfigModule for CacheConfigService
    ConfigModule.forRoot({ isGlobal: true }),
    // Register AccessControlModule for RoleGuard
    AccessControlModule.forRoles(RBAC_ROLES, {}),
    // Register Redis module before other modules that depend on it
    // Uses strict environment-based configuration (no fallbacks for non-local environments)
    RedisModule.registerAsync({
      inject: [EnvConfig],
      imports: [TypedConfigModule],
      useFactory: (envConfig: EnvConfig) => {
        const nodeEnv = process.env.NODE_ENV || 'local';
        const isLocal = nodeEnv === 'local';

        // Validate Redis configuration for non-local environments
        if (!isLocal) {
          validateRedisConfigForEnvironment(envConfig, nodeEnv);
        }

        const mode = (
          isLocal ? envConfig.REDIS_MODE || 'single' : envConfig.REDIS_MODE
        ) as 'single' | 'cluster' | 'auto';

        const config: any = {
          mode,
          healthCheckInterval: isLocal
            ? envConfig.REDIS_HEALTHCHECK_INTERVAL_MS || 30000
            : envConfig.REDIS_HEALTHCHECK_INTERVAL_MS,
          fallbackToSingle: true,
        };

        // Create single configuration with strict environment separation
        config.single = {
          host: isLocal
            ? envConfig.REDIS_HOST || 'localhost'
            : envConfig.REDIS_HOST,
          port: isLocal ? envConfig.REDIS_PORT || 6379 : envConfig.REDIS_PORT,
          password: envConfig.REDIS_PASSWORD,
          username: envConfig.REDIS_USERNAME,
          db: isLocal ? envConfig.REDIS_DB || 0 : envConfig.REDIS_DB,
        };

        // Only add TLS if explicitly enabled
        if (envConfig.REDIS_TLS === true) {
          config.single.tls = {};
        }

        return config;
      },
    }),
    CacheModule,
    EnhancedNotificationModule,
  ],
  providers: [
    NotificationSeedService,
    NotificationSeedCommand,
    EventNotificationSeedService,
    EventNotificationSeedCommand,
    AllNotificationsSeedCommand,
    EventNotificationSeed,
    SelectionNotificationSeedService,
    SelectionNotificationSeedCommand,
  ],
  exports: [
    NotificationSeedService,
    EventNotificationSeedService,
    SelectionNotificationSeedService,
  ],
})
export class SeedModule {}
