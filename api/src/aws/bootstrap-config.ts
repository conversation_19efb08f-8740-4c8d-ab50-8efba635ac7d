/**
 * AWS Parameter Store Configuration Bootstrap Module
 *
 * This module provides a TypeScript-safe configuration bootstrap system that:
 * - Loads configuration from AWS Parameter Store for production environments
 * - Falls back to .env files for development and staging environments
 * - Provides strict type safety with proper error handling
 * - Returns detailed configuration metadata for monitoring
 *
 * Key improvements in this version:
 * - Strong TypeScript typing throughout
 * - Proper error type handling
 * - Return value with configuration metadata
 * - Environment validation with type constraints
 * - Comprehensive error logging with structured details
 *
 * <AUTHOR> for TypeScript compliance
 * @version 2.0.0
 */
import { ParameterStoreConfigProvider } from './parameter-store-config.provider';
import * as dotenv from 'dotenv';

/**
 * Environment types supported by the application
 */
type Environment = 'development' | 'staging' | 'production';

/**
 * Configuration result interface
 */
interface ConfigurationResult {
  source: 'parameter-store' | 'env-file' | 'fallback';
  parametersLoaded: number;
  environment: string;
}

/**
 * AWS Parameter Store error interface
 */
interface ParameterStoreError extends Error {
  code?: string;
  Code?: string;
}

/**
 * Bootstrap the application configuration by loading from AWS Parameter Store
 * or falling back to .env files based on the environment
 */
export async function bootstrapConfig(): Promise<ConfigurationResult> {
  console.log('🔧 Bootstrap: Initializing configuration loading...');

  // First, load .env file to get NODE_ENV if not already set
  if (!process.env.NODE_ENV) {
    console.log(
      '🔧 NODE_ENV not set, loading .env file to determine environment...',
    );
    dotenv.config();
  }

  const nodeEnv = process.env.NODE_ENV;
  console.log(`📋 Environment: ${nodeEnv || 'undefined'}`);

  // Define supported environments for Parameter Store loading
  const supportedEnvironments: Environment[] = [
    'development',
    'staging',
    'production',
  ];
  const isParameterStoreEnvironment =
    nodeEnv &&
    supportedEnvironments.includes(nodeEnv.toLowerCase() as Environment);

  if (isParameterStoreEnvironment) {
    const envDisplayName =
      nodeEnv.charAt(0).toUpperCase() + nodeEnv.slice(1).toLowerCase();
    console.log(`🏭 ${envDisplayName} environment detected`);
    console.log(`📡 Parameter Store loading enabled for ${nodeEnv}`);

    if (nodeEnv === 'production') {
      console.log('📡 Parameter Store loading is MANDATORY for production');
      console.log(
        '🚨 Application will NOT start if Parameter Store fails to load',
      );
    } else {
      console.log(
        '📡 Parameter Store loading with fallback to .env for non-production',
      );
    }

    console.log('📡 Loading configuration from AWS Parameter Store...');

    try {
      // Load from Parameter Store (mandatory for production, fallback for others)
      const parameterStoreConfig =
        await ParameterStoreConfigProvider.loadParametersAndMergeWithEnv();

      // Override process.env with Parameter Store values
      Object.assign(process.env, parameterStoreConfig);

      // Verify critical values were loaded from Parameter Store
      const port = process.env.PORT;
      const dbUrl = process.env.DATABASE_URL;
      const awsRegion = process.env.AWS_REGION;

      console.log('✅ Parameter Store configuration loaded successfully');
      console.log(`   PORT from Parameter Store: ${port}`);
      console.log(`   DATABASE_URL: ${dbUrl ? '[SET]' : '[NOT SET]'}`);
      console.log(`   AWS_REGION: ${awsRegion}`);

      const parametersCount = Object.keys(parameterStoreConfig).length;
      console.log(`   Total parameters loaded: ${parametersCount}`);

      // Validate critical environment variables
      validateEnvironmentVariables(nodeEnv);

      console.log('🎯 Configuration loading completed successfully');
      console.log(`   Final PORT value: ${process.env.PORT}`);
      console.log(`   Final NODE_ENV value: ${process.env.NODE_ENV}`);

      return {
        source: 'parameter-store',
        parametersLoaded: parametersCount,
        environment: nodeEnv,
      };
    } catch (error: unknown) {
      const parameterStoreError = error as ParameterStoreError;

      if (nodeEnv === 'production') {
        // CRITICAL FAILURE - Parameter Store is mandatory in production
        console.error(
          '💥 CRITICAL ERROR: Parameter Store loading failed in production environment',
        );
        console.error(
          '🚨 Application startup ABORTED - Parameter Store is mandatory for production',
        );
        console.error('');
        console.error('📋 Error Details:');
        console.error(
          `   Error Type: ${parameterStoreError?.name || 'Unknown'}`,
        );
        console.error(
          `   Error Message: ${parameterStoreError?.message || 'No message available'}`,
        );
        console.error(
          `   Error Code: ${parameterStoreError?.code || parameterStoreError?.Code || 'No code available'}`,
        );
        console.error('');
        console.error('🔍 Parameter Store Configuration:');
        console.error(`   Path: /react/prod/api/`);
        console.error(`   Region: ${process.env.AWS_REGION || 'eu-west-1'}`);
        console.error(`   Environment: ${nodeEnv}`);
        console.error('');
        console.error('🛠️  Troubleshooting Steps:');
        console.error('   1. Verify AWS credentials and IAM permissions');
        console.error('   2. Check Parameter Store path: /react/prod/api/');
        console.error('   3. Ensure parameters exist in eu-west-1 region');
        console.error(
          '   4. Verify network connectivity to AWS Parameter Store',
        );
        console.error(
          '   5. Check CloudWatch logs for detailed AWS API errors',
        );
        console.error('');
        console.error('❌ Application startup failed - exiting with code 1');

        // Exit immediately - NO fallback in production
        process.exit(1);
      } else {
        // Non-production: Log error and fallback to .env
        console.warn(
          `⚠️  Parameter Store loading failed for ${nodeEnv} environment`,
        );
        console.warn('📄 Falling back to .env file configuration...');
        console.warn('');
        console.warn('📋 Error Details:');
        console.warn(
          `   Error Type: ${parameterStoreError?.name || 'Unknown'}`,
        );
        console.warn(
          `   Error Message: ${parameterStoreError?.message || 'No message available'}`,
        );
        console.warn('');

        // Load .env file as fallback
        dotenv.config();
        console.log('✅ .env fallback configuration loaded successfully');

        // Validate critical environment variables
        validateEnvironmentVariables(nodeEnv);

        console.log('🎯 Configuration loading completed successfully');
        console.log(`   Final PORT value: ${process.env.PORT}`);
        console.log(`   Final NODE_ENV value: ${process.env.NODE_ENV}`);

        return {
          source: 'fallback',
          parametersLoaded: 0,
          environment: nodeEnv,
        };
      }
    }
  } else {
    console.log(`🔧 ${nodeEnv || 'Local'} environment detected`);
    console.log('📄 Loading configuration from .env file only...');

    // Load .env file for non-Parameter Store environments
    dotenv.config();

    console.log('✅ .env configuration loaded successfully');
    console.log(`   PORT from .env: ${process.env.PORT}`);

    // Validate critical environment variables
    validateEnvironmentVariables(nodeEnv || 'local');

    console.log('🎯 Configuration loading completed successfully');
    console.log(`   Final PORT value: ${process.env.PORT}`);
    console.log(`   Final NODE_ENV value: ${process.env.NODE_ENV}`);

    return {
      source: 'env-file',
      parametersLoaded: 0,
      environment: nodeEnv || 'local',
    };
  }
}

/**
 * Validate that critical environment variables are set
 */
function validateEnvironmentVariables(environment: string): void {
  // Final validation
  if (!process.env.PORT) {
    const errorMsg = `PORT environment variable is required but not set (Environment: ${environment})`;
    console.error(`❌ Configuration validation failed: ${errorMsg}`);
    throw new Error(errorMsg);
  }

  if (!process.env.NODE_ENV) {
    const errorMsg = 'NODE_ENV environment variable is required but not set';
    console.error(`❌ Configuration validation failed: ${errorMsg}`);
    throw new Error(errorMsg);
  }
}
