import { Logger } from '@nestjs/common';
import { ParameterStoreConfigProvider } from './parameter-store-config.provider';
import * as dotenv from 'dotenv';

type Environment = 'development' | 'staging' | 'production';

interface ConfigurationResult {
  source: 'parameter-store' | 'env-file' | 'fallback';
  parametersLoaded: number;
  environment: string;
}

interface ParameterStoreError extends Error {
  code?: string;
  Code?: string;
  statusCode?: number;
  $metadata?: {
    httpStatusCode?: number;
    requestId?: string;
  };
}

/**
 * Bootstrap the application configuration by loading from AWS Parameter Store
 * or falling back to .env files based on the environment
 */
export async function bootstrapConfig(): Promise<ConfigurationResult> {
  const logger = new Logger('BootstrapConfig');
  const isDevelopment =
    process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'local';

  logger.log('Initializing configuration loading...');

  // First, load .env file to get NODE_ENV if not already set
  if (!process.env.NODE_ENV) {
    if (isDevelopment) {
      logger.debug(
        'NODE_ENV not set, loading .env file to determine environment...',
      );
    }
    dotenv.config();
  }

  const nodeEnv = process.env.NODE_ENV;
  logger.log(`Environment: ${nodeEnv || 'undefined'}`);

  // Define supported environments for Parameter Store loading
  const supportedEnvironments: Environment[] = [
    'development',
    'staging',
    'production',
  ];
  const isParameterStoreEnvironment =
    nodeEnv &&
    supportedEnvironments.includes(nodeEnv.toLowerCase() as Environment);

  if (isParameterStoreEnvironment) {
    const envDisplayName =
      nodeEnv.charAt(0).toUpperCase() + nodeEnv.slice(1).toLowerCase();
    logger.log(`${envDisplayName} environment detected`);

    if (isDevelopment) {
      logger.debug(`Parameter Store loading enabled for ${nodeEnv}`);
    }

    if (nodeEnv === 'production') {
      logger.log('Parameter Store loading is MANDATORY for production');
      logger.warn(
        'Application will NOT start if Parameter Store fails to load',
      );
    } else if (isDevelopment) {
      logger.debug(
        'Parameter Store loading with fallback to .env for non-production',
      );
    }

    logger.log('Loading configuration from AWS Parameter Store...');

    try {
      // Load from Parameter Store (mandatory for production, fallback for others)
      const parameterStoreConfig =
        await ParameterStoreConfigProvider.loadParametersAndMergeWithEnv();

      // Override process.env with Parameter Store values
      Object.assign(process.env, parameterStoreConfig);

      // Verify critical values were loaded from Parameter Store
      const port = process.env.PORT;
      const dbUrl = process.env.DATABASE_URL;
      const awsRegion = process.env.AWS_REGION;
      const parametersCount = Object.keys(parameterStoreConfig).length;

      logger.log('Parameter Store configuration loaded successfully');

      if (isDevelopment) {
        logger.debug(`PORT from Parameter Store: ${port}`);
        logger.debug(`DATABASE_URL: ${dbUrl ? '[SET]' : '[NOT SET]'}`);
        logger.debug(`AWS_REGION: ${awsRegion}`);
        logger.debug(`Total parameters loaded: ${parametersCount}`);
      }

      // Validate critical environment variables
      validateEnvironmentVariables(nodeEnv);

      logger.log('Configuration loading completed successfully');

      if (isDevelopment) {
        logger.debug(`Final PORT value: ${process.env.PORT}`);
        logger.debug(`Final NODE_ENV value: ${process.env.NODE_ENV}`);
      }

      return {
        source: 'parameter-store',
        parametersLoaded: parametersCount,
        environment: nodeEnv,
      };
    } catch (error: unknown) {
      const parameterStoreError = error as ParameterStoreError;

      if (nodeEnv === 'production') {
        // CRITICAL FAILURE - Parameter Store is mandatory in production
        logger.error(
          'CRITICAL ERROR: Parameter Store loading failed in production environment',
        );
        logger.error(
          'Application startup ABORTED - Parameter Store is mandatory for production',
        );

        logger.error('Parameter Store loading failed', {
          errorType: parameterStoreError?.name || 'Unknown',
          errorMessage: parameterStoreError?.message || 'No message available',
          errorCode:
            parameterStoreError?.code ||
            parameterStoreError?.Code ||
            'No code available',
          parameterStorePath: '/react/prod/api/',
          region: process.env.AWS_REGION || 'eu-west-1',
          environment: nodeEnv,
          troubleshooting: [
            'Verify AWS credentials and IAM permissions',
            'Check Parameter Store path: /react/prod/api/',
            'Ensure parameters exist in eu-west-1 region',
            'Verify network connectivity to AWS Parameter Store',
            'Check CloudWatch logs for detailed AWS API errors',
          ],
        });

        logger.error('Application startup failed - exiting with code 1');

        // Exit immediately - NO fallback in production
        process.exit(1);
      } else {
        // Non-production: Log error and fallback to .env
        logger.warn(
          `Parameter Store loading failed for ${nodeEnv} environment`,
        );
        logger.warn('Falling back to .env file configuration...');

        if (isDevelopment) {
          logger.warn('Parameter Store error details', {
            errorType: parameterStoreError?.name || 'Unknown',
            errorMessage:
              parameterStoreError?.message || 'No message available',
          });
        }

        // Load .env file as fallback
        dotenv.config();
        logger.log('.env fallback configuration loaded successfully');

        // Validate critical environment variables
        validateEnvironmentVariables(nodeEnv);

        logger.log('Configuration loading completed successfully');

        if (isDevelopment) {
          logger.debug(`Final PORT value: ${process.env.PORT}`);
          logger.debug(`Final NODE_ENV value: ${process.env.NODE_ENV}`);
        }

        return {
          source: 'fallback',
          parametersLoaded: 0,
          environment: nodeEnv,
        };
      }
    }
  } else {
    logger.log(`${nodeEnv || 'Local'} environment detected`);

    if (isDevelopment) {
      logger.debug('Loading configuration from .env file only...');
    }

    // Load .env file for non-Parameter Store environments
    dotenv.config();

    logger.log('.env configuration loaded successfully');

    if (isDevelopment) {
      logger.debug(`PORT from .env: ${process.env.PORT}`);
    }

    // Validate critical environment variables
    validateEnvironmentVariables(nodeEnv || 'local');

    logger.log('Configuration loading completed successfully');

    if (isDevelopment) {
      logger.debug(`Final PORT value: ${process.env.PORT}`);
      logger.debug(`Final NODE_ENV value: ${process.env.NODE_ENV}`);
    }

    return {
      source: 'env-file',
      parametersLoaded: 0,
      environment: nodeEnv || 'local',
    };
  }
}

/**
 * Validate that critical environment variables are set
 */
function validateEnvironmentVariables(environment: string): void {
  const logger = new Logger('BootstrapConfig');

  // Final validation
  if (!process.env.PORT) {
    const errorMsg = `PORT environment variable is required but not set (Environment: ${environment})`;
    logger.error(`Configuration validation failed: ${errorMsg}`);
    throw new Error(errorMsg);
  }

  if (!process.env.NODE_ENV) {
    const errorMsg = 'NODE_ENV environment variable is required but not set';
    logger.error(`Configuration validation failed: ${errorMsg}`);
    throw new Error(errorMsg);
  }
}
